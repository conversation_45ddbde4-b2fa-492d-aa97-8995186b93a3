package com.gwssi.framework.svc.reporting;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;

/**
 * Created on 2020/1/27
 *
 * <AUTHOR>
 *
 */
@Configuration
@EnableResourceServer
public class ReportingServerConfig extends ResourceServerConfigurerAdapter {

    @Value("${security.access.permit:hasIpAddress('*********/16') or hasIpAddress('**********/16') or hasIpAddress('**********/8') or hasIpAddress('127.0.0.1') or hasIpAddress('localhost')}")
    private String permit;

    // 用户自定义的微服务白名单，多个请求逗号分开。不要加url参数！
    @Value("${security.access.svc-permit-urls:/api/sso/v1/login/demo1.do,/api/sso/v1/login/demo2.do}")
    private String[] svcPermitUrls;

    @Override
    public void configure(HttpSecurity http) throws Exception {

        String[] defaultPermitUrls = {"/api/common/v1/common/alive.do"};

        String[] permitUrls = ArrayUtils.addAll(defaultPermitUrls, svcPermitUrls);

        // 每个服务增加白名单
//        http.antMatcher("/**")
//                .authorizeRequests().antMatchers(permitUrls).permitAll();
//
//        http.antMatcher("/**").cors().and()
//                .authorizeRequests().anyRequest().authenticated()
//                .and()
//                .authorizeRequests().anyRequest()
//                .access(permit);

        http.antMatcher("/**").cors().and().authorizeRequests().anyRequest().permitAll();
    }

}
