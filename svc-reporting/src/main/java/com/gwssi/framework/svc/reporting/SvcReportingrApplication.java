package com.gwssi.framework.svc.reporting;

import com.gwssi.optimus.core.context.SpringContextHelper;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.context.ApplicationPidFileWriter;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@SpringBootApplication(scanBasePackages = {"com.gwssi"}, exclude = {MongoAutoConfiguration.class})
@EnableDiscoveryClient
public class SvcReportingrApplication extends SpringBootServletInitializer implements ApplicationListener<ContextRefreshedEvent> {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(SvcReportingrApplication.class);
        application.addListeners(new ApplicationPidFileWriter());
        application.run(args);
    }

    @RequestMapping("/alive")
    public String alive() {
        return "Welcome to the world of Spring Boot!";
    }


    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        SpringContextHelper.setApplicationContext(event.getApplicationContext());
    }
}
