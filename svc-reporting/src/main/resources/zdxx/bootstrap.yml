nacos:
  server: 1.203.97.112:8848
  namespase: sz-zdxx
  group: ZDXX
spring:
  cloud:
    nacos:
      config:
        username: nacos
        password: Abc1!2@3!@Gwssi
        server-addr: ${nacos.server}
        namespace: ${nacos.namespase}
        group: ${nacos.group}
        file-extension: yaml
        ext-config:
          - data-id: focus-common-${spring.profiles.active}.yaml
            group: ${nacos.group}
            refresh: true
        shared-configs[0]:
          data-id: focus-common.yaml
          #          group: ${nacos.group}
          refresh: true
        shared-configs[1]:
          data-id: ${spring.application.name}-common.yaml
          #          group: ${nacos.group}
          refresh: true
  application:
    name: reporting

  profiles:
    active: zdxx
logging:
  level:
    com.alibaba.cloud.nacos: debug
#是否开启多租户
multitenancy:
  enabled: true