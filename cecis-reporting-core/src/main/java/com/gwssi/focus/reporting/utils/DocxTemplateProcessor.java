package com.gwssi.focus.reporting.utils;

/**
 * <AUTHOR>
 * @Date 2025/6/6 10:13
 * @Description: DocxTemplateProcessor
 */
import org.apache.poi.xwpf.model.XWPFHeaderFooterPolicy;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTR;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTRPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTText;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DocxTemplateProcessor {

    public static void main(String[] args) throws Exception {

        String inputPath = "D:\\gwdata\\验厂模型汇报-九影（深圳）电器有限公司.docx";
        String outputPath = "D:\\gwdata\\output.docx";
        Map<String, String> placeholderMap = new HashMap<>(); // 标识位与替换内容的映射
        placeholderMap.put("{姓名}", "张三");
        placeholderMap.put("{日期}", "2024年5月1日");
        placeholderMap.put("{部门}", "技术部");

        // 要插入的多段文本内容（可自定义样式）
        List<String> paragraphsToInsert = Arrays.asList(
                "尊敬的{name}：",
                "    您好！",
                "    兹通知您，{日期}起您的所属部门调整为{部门}，请知悉。",
                "    如有疑问，请联系HR邮箱：<EMAIL>。"
        );

        try (XWPFDocument document = new XWPFDocument(new FileInputStream(inputPath))) {
            // 步骤1：替换所有标识位
            replacePlaceholders(document, placeholderMap);

            // 步骤2：在文档末尾插入多段文本（可根据需求调整插入位置）
            insertMultipleParagraphs(document, paragraphsToInsert);

            // 保存文档
            document.write(new FileOutputStream(outputPath));
        }
    }

    /**
     * 替换文档中所有标识位（如 {姓名}）
     */
    private static void replacePlaceholders(XWPFDocument document, Map<String, String> placeholderMap) {
        // 遍历所有段落
        for (XWPFParagraph para : document.getParagraphs()) {
            replacePlaceholdersInParagraph(para, placeholderMap);
        }
    }

    /**
     * 替换单个段落中的标识位
     */
    private static void replacePlaceholdersInParagraph(XWPFParagraph para, Map<String, String> placeholderMap) {
        List<XWPFRun> runs = para.getRuns();
        if (runs == null) return;

        // 遍历段落中的每个运行（XWPFRun），查找标识位
        for (int i = 0; i < runs.size(); i++) {
            XWPFRun run = runs.get(i);
            CTR ctr = run.getCTR();
            String text = run.getText(0); // 获取当前运行的文本（可能为null）
            if (text == null) continue;

            // 检查文本中是否包含任意标识位（如 {姓名}）
            boolean hasPlaceholder = false;
            for (String placeholder : placeholderMap.keySet()) {
                if (text.contains(placeholder)) {
                    hasPlaceholder = true;
                    // 替换标识位
                    String newText = text.replace(placeholder, placeholderMap.get(placeholder));
                    run.setText(newText, 0); // 直接替换当前运行的文本

                    run.addBreak();
                    run.setText("建议进一步核查以下事项：");
                    run.addBreak();
                    run.setText("建议进一步核查以下事项：\n" +
                            "\t•\t要求工厂说明缺失核心物料的采购原因，是自有库存消耗，还是委外加工、代工生产；\n" +
                            "\t•\t调取ERP系统物料采购记录、库存进出记录，核查是否存在非发票流入的原料；\n" +
                            "\t•\t若企业声称由外协或集团内企业统一采购，必须提供《委托加工协议》《入库单》等支撑文件；\n" +
                            "\t•\t如确实无采购也无委托痕迹，提示风险较高，建议评定为“不通过”\t\n" +
                            "•\t可延伸审查产成品库存是否异常增长，防止通过虚构库存掩盖无采购实情。\n");
                    // 注意：若替换后文本跨多个运行（如原文本被分割），需额外处理
                    // 此处简化处理，假设标识位在一个运行内
                }
            }

            // 若当前运行被修改，可能需要合并相邻的运行（可选）
            // （POI 会自动处理部分样式合并，复杂情况需手动调整）
        }
    }

    /**
     * 在文档中插入多段文本（可自定义每段样式）
     */
    private static void insertMultipleParagraphs(XWPFDocument document, List<String> paragraphs) {
        // 插入位置：文档末尾（可根据需求调整，如插入到某个段落之后）
        int insertPosition = document.getParagraphs().size();

        for (String paraText : paragraphs) {
            XWPFParagraph newPara = document.createParagraph();

            // 设置段落格式（可选）
            newPara.setAlignment(ParagraphAlignment.LEFT); // 左对齐
            newPara.setSpacingAfter(100); // 段落后间距

            // 创建运行（XWPFRun）并设置文本样式
            XWPFRun run = newPara.createRun();
            run.setText(paraText);
            run.setFontSize(12); // 字号12磅
            run.setFontFamily("宋体"); // 字体宋体

            // 可选：为特定段落设置特殊样式（如标题）
            if (paraText.startsWith("尊敬的")) {
                newPara.setAlignment(ParagraphAlignment.CENTER); // 居中对齐
                run.setBold(true); // 加粗
                run.setFontSize(16); // 字号16磅
            }
        }
    }
}