package com.gwssi.focus.reporting;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;

/**
 * <AUTHOR>
 * @Date 2025/5/20 15:10
 * @Description: ExcelTest
 */
public class ExcelTest {
    /**
     * 修改表格：更新第二行，添加第三行，并合并相邻且内容相同的单元格
     */
    public static void modifyTable(XWPFTable table) {
        // 确保表格至少有3行
        ensureTableHasRows(table, 3);

        // 更新第二行内容
        updateSecondRow(table);

        // 添加第三行
        addThirdRow(table);

        // 合并第二行和第三行中相邻且内容相同的单元格
//        mergeAdjacentCellsWithSameContent(table, 1, 2); // 1和2分别代表第二行和第三行(0-based)
    }

    /**
     * 确保表格至少有指定数量的行
     */
    private static void ensureTableHasRows(XWPFTable table, int rowCount) {
        while (table.getNumberOfRows() < rowCount) {
            table.createRow();
        }
    }

    /**
     * 更新第二行内容（示例：设置为"Row 2, Cell X"）
     */
    private static void updateSecondRow(XWPFTable table) {
        XWPFTableRow secondRow = table.getRow(1); // 第二行(0-based)

        // 确保第二行有足够的单元格
        ensureRowHasCells(secondRow, table.getRow(0).getTableCells().size());

        // 更新第二行每个单元格的内容
        for (int i = 0; i < secondRow.getTableCells().size(); i++) {
            XWPFTableCell cell = secondRow.getCell(i);
            cell.setText("Row 2, Cell " + (i + 1));
        }
    }

    /**
     * 添加第三行（示例：设置为"Row 3, Cell X"或与第二行相同的内容）
     */
    private static void addThirdRow(XWPFTable table) {
        XWPFTableRow thirdRow = table.getRow(2); // 第三行(0-based)

        // 确保第三行有足够的单元格
        ensureRowHasCells(thirdRow, table.getRow(0).getTableCells().size());

        // 设置第三行内容（示例：让部分单元格内容与第二行相同，以便合并）
        for (int i = 0; i < thirdRow.getTableCells().size(); i++) {
            XWPFTableCell cell = thirdRow.getCell(i);

            // 示例：让第三行的偶数单元格内容与第二行相同
            if (i % 2 == 0) {
                cell.setText("Row 2, Cell " + (i + 1)); // 与第二行相同内容，以便合并
            } else {
                cell.setText("Row 3, Cell " + (i + 1));
            }
        }
    }

    /**
     * 确保行有足够的单元格
     */
    private static void ensureRowHasCells(XWPFTableRow row, int cellCount) {
        while (row.getTableCells().size() < cellCount) {
            row.addNewTableCell();
        }
    }

    /**
     * 合并指定两行中相邻且内容相同的单元格（修正版）
     */
    private static void mergeAdjacentCellsWithSameContent(XWPFTable table, int rowIndex1, int rowIndex2) {
        XWPFTableRow row1 = table.getRow(rowIndex1);
        XWPFTableRow row2 = table.getRow(rowIndex2);

        // 确保两行有相同数量的单元格
        int cellCount = Math.min(row1.getTableCells().size(), row2.getTableCells().size());

        // 遍历每个单元格，检查是否可以合并
        for (int i = 0; i < cellCount; i++) {
            XWPFTableCell cell1 = row1.getCell(i);
            XWPFTableCell cell2 = row2.getCell(i);

            // 获取单元格文本
            String text1 = getCellText(cell1);
            String text2 = getCellText(cell2);

            // 如果内容相同，则合并单元格
            if (text1.equals(text2)) {
                // 合并垂直方向的单元格
                mergeCellsVertically(table, i, rowIndex1, rowIndex2);
            }
        }
    }

    /**
     * 垂直合并表格中的单元格
     */
    private static void mergeCellsVertically(XWPFTable table, int col, int fromRow, int toRow) {
        for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
            XWPFTableCell cell = table.getRow(rowIndex).getCell(col);

            // 只设置第一个单元格为"继续"，其他单元格设置为"已合并"
            if (rowIndex == fromRow) {
                // 第一个单元格
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
            } else {
                // 后续合并的单元格
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            }
        }
    }

    /**
     * 获取单元格文本内容
     */
    private static String getCellText(XWPFTableCell cell) {
        StringBuilder text = new StringBuilder();
        for (XWPFParagraph para : cell.getParagraphs()) {
            for (XWPFRun run : para.getRuns()) {
                if (run.getText(0) != null) {
                    text.append(run.getText(0));
                }
            }
        }
        return text.toString().trim();
    }

}
