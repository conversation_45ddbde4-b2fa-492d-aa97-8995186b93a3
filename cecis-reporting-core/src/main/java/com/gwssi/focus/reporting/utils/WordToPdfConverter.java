package com.gwssi.focus.reporting.utils;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;

import java.io.*;

public class WordToPdfConverter {

    /**
     * 将 Word 文档转换为 PDF
     * @param wordPath 输入的 Word 文档路径
     * @param pdfPath 输出的 PDF 文件路径
     * @return 转换后的 PDF 文件路径
     * @throws IOException 如果文件操作失败
     */
    public static String convertWordToPdf(String wordPath, String pdfPath) throws IOException {
        try (FileInputStream fis = new FileInputStream(wordPath);
             XWPFDocument document = new XWPFDocument(fis);
             PDDocument pdf = new PDDocument()) {

            // 加载支持中文的字体（需要将字体文件放在类路径下）
            // 示例：使用系统字体（需确保运行环境存在该字体）
//            PDType0Font font = PDType0Font.load(pdf, new File("src/main/resources/font/SimSun.ttf"));

            // 使用类加载器加载字体资源
            InputStream fontStream = WordToPdfConverter.class.getClassLoader()
                    .getResourceAsStream("fonts/SimSun.ttf");

            if (fontStream == null) {
                throw new FileNotFoundException("无法在类路径下找到字体文件: fonts/SimSun.ttf");
            }

            // 从输入流加载字体
            PDType0Font font = PDType0Font.load(pdf, fontStream, true);


            for (XWPFParagraph paragraph : document.getParagraphs()) {
                PDPage page = new PDPage();
                pdf.addPage(page);

                try (PDPageContentStream contentStream =
                             new PDPageContentStream(pdf, page, PDPageContentStream.AppendMode.APPEND, true)) {

                    contentStream.setFont(font, 12); // 使用自定义字体
                    contentStream.beginText();
                    contentStream.newLineAtOffset(50, 700);

                    String text = paragraph.getText();
                    contentStream.showText(text);
                    contentStream.endText();
                }
            }

            try (FileOutputStream fos = new FileOutputStream(pdfPath)) {
                pdf.save(fos);
            }

            return pdfPath;
        }
    }

    public static String convertWordToPdf1(String wordPath, String pdfPath) throws IOException {
        try (FileInputStream fis = new FileInputStream(wordPath);
             XWPFDocument document = new XWPFDocument(fis);
             PDDocument pdf = new PDDocument()) {

            // 遍历 Word 文档的每个段落
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                PDPage page = new PDPage();
                pdf.addPage(page);

                try (PDPageContentStream contentStream =
                             new PDPageContentStream(pdf, page)) {

                    contentStream.setFont(PDType1Font.HELVETICA, 12);
                    contentStream.beginText();
                    contentStream.newLineAtOffset(50, 700);

                    // 提取段落文本并写入 PDF
                    String text = paragraph.getText();
                    contentStream.showText(text);
                    contentStream.endText();
                }
            }

            // 保存 PDF 文件
            try (FileOutputStream fos = new FileOutputStream(pdfPath)) {
                pdf.save(fos);
            }

            return pdfPath;
        }
    }


    public static void main(String[] args) {
        try {
            String wordPath = "input.docx";
            String pdfPath = "output.pdf";
            String result = convertWordToPdf(wordPath, pdfPath);
            System.out.println("转换成功，PDF 路径：" + result);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}