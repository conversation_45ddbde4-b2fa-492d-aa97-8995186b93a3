package com.gwssi.focus.reporting.utils;

import com.gwssi.focus.reporting.auditreport.impl.BusinessProcessing;
import com.gwssi.optimus.core.exception.OptimusException;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.io.*;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/6/6 11:15
 * @Description: DocumentOperator
 */
public class DocumentOperator {


    /**
     * 处理Word文档中的指定表格
     * @param inputPath  输入文件路径
     * @param outputPath 输出文件路径
     * @param tableIndexs 要处理的表格索引（从0开始）
     * @param fontSize 字体大小
     */
    public static void changeWordTableStyle(String inputPath, String outputPath, List<Integer> tableIndexs, int fontSize) {
        try (InputStream is = new FileInputStream(inputPath);
             XWPFDocument document = new XWPFDocument(is)) {
            for (int i = 0; i < tableIndexs.size(); i++) {
                // 获取指定表格
                XWPFTable targetTable = document.getTables().get(tableIndexs.get(i));
                // 设置表格字体为小五（10.5磅）
                setTableFontSize(targetTable, fontSize);
            }
            // 保存修改后的文档
            try (OutputStream os = new FileOutputStream(outputPath)) {
                document.write(os);
            }

        } catch (IOException e) {
            throw new RuntimeException("处理Word文档时发生错误", e);
        }
    }

    /**
     * 设置表格所有单元格的字体大小
     * @param table    目标表格
     * @param fontSize 字体大小（单位：磅）
     */
    private static void setTableFontSize(XWPFTable table, int fontSize) {
        // 获取表格的总行数
        int rowCount = table.getRows().size();

        // 从第1行(index=1)开始遍历，跳过第0行（表头）
        for (int i = 1; i < rowCount; i++) {
            XWPFTableRow row = table.getRow(i);
            for (XWPFTableCell cell : row.getTableCells()) {
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    for (XWPFRun run : paragraph.getRuns()) {
                        run.setFontSize(fontSize); // 设置字体大小
                        // run.setFontFamily("宋体"); // 可选：设置中文字体
                    }
                }
            }
        }
    }

    private static void deleteTableByIndex(XWPFDocument document, int tableIndex) {
        List<XWPFTable> tables = document.getTables();
        if (tableIndex < tables.size()) {
            XWPFTable targetTable = tables.get(tableIndex);
            int tablePosition = document.getPosOfTable(targetTable);
            document.removeBodyElement(tablePosition);
        }
    }
    /**
     * 删除文字
     */
    public static void deleteParagraphByText(XWPFDocument document, String deleteText) {
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = paragraphs.size() - 1; i >= 0; i--) { // 倒序删除防止索引变化
            XWPFParagraph paragraph = paragraphs.get(i);
            System.out.println(paragraph.getText());
            System.out.println("-----------------------------------------------------------------------------------");
            if (paragraph.getText().contains(deleteText)) {
                int paragraphPosition = document.getPosOfParagraph(paragraph);
                document.removeBodyElement(paragraphPosition);
//                System.out.println("删除段落内容：" + paragraph.getText());
            }
        }
    }


    /**
     * 替换文档中所有标识位（如 {姓名}）
     */
    public static void replacePlaceholders(XWPFDocument document, String placeholder,List<String> paragraphs) {
        // 遍历所有段落
        for (XWPFParagraph para : document.getParagraphs()) {
            replacePlaceholdersInParagraph(para, placeholder,paragraphs);
        }
    }


    /**
     * 替换单个段落中的标识位
     */
    private static void replacePlaceholdersInParagraph(XWPFParagraph para, String placeholder, List<String> paragraphs) {
        List<XWPFRun> runs = para.getRuns();
        if (runs == null) return;
        if (para.getText().contains(placeholder)){
            clearParagraphText(para);
            XWPFRun run = para.createRun();
            for (String newText:paragraphs){
                run.setText(newText); // 直接替换当前运行的文本
                run.setFontSize(12); // 字号12磅 小四
                run.addBreak();
            }
        }

    }


    public static boolean importWordTable(XWPFDocument document,String placeholder, String wordPath, int sourceTableIndex, BusinessProcessing businessProcessing,Map<String, Object> parameters) throws OptimusException {
        parameters.put("placeholder",placeholder);
        parameters.put("wordPath",wordPath);
        parameters.put("sourceTableIndex",sourceTableIndex);
        parameters.put("document",document);
        boolean process = businessProcessing.process(null, parameters);
        return process;
    }


    /**
     * 根据文本内容查找段落
     */
    public static XWPFParagraph findParagraphByText(XWPFDocument doc, String text) {
        for (XWPFParagraph para : doc.getParagraphs()) {
            if (para.getText().contains(text)) {
                return para;
            }
        }
        return null;
    }

    /**
     * 清除段落文本
     */
    public static void clearParagraphText(XWPFParagraph para) {
        for (int i = para.getRuns().size() - 1; i >= 0; i--) {
            para.removeRun(i);
        }
        para.createRun().setText(""); // 保留空运行以维持段落结构
    }
    /**
     * 复制表格内容（修正版）
     */
    public static void copyTableContent(XWPFTable sourceTable, XWPFTable targetTable) {
        // 复制表格属性
        CTTblPr sourceTblPr = sourceTable.getCTTbl().getTblPr();
        if (sourceTblPr != null) {
            targetTable.getCTTbl().setTblPr((CTTblPr) sourceTblPr.copy());
        }

        // 复制行和单元格
        for (int i = 0; i < sourceTable.getRows().size(); i++) {
            XWPFTableRow sourceRow = sourceTable.getRow(i);
            XWPFTableRow targetRow = (i == 0) ? targetTable.getRow(0) : targetTable.createRow();

            // 复制行属性
            CTTrPr sourceTrPr = sourceRow.getCtRow().getTrPr();
            if (sourceTrPr != null) {
                targetRow.getCtRow().setTrPr((CTTrPr) sourceTrPr.copy());
            }

            // 确保目标行有足够的单元格
            while (targetRow.getTableCells().size() < sourceRow.getTableCells().size()) {
                targetRow.addNewTableCell();
            }

            // 复制单元格内容和样式
            for (int j = 0; j < sourceRow.getTableCells().size(); j++) {
                XWPFTableCell sourceCell = sourceRow.getCell(j);
                XWPFTableCell targetCell = targetRow.getCell(j);

                // 复制单元格属性
                CTTcPr sourceTcPr = sourceCell.getCTTc().getTcPr();
                if (sourceTcPr != null) {
                    targetCell.getCTTc().setTcPr((CTTcPr) sourceTcPr.copy());
                }

                // 清空目标单元格（修正版）
                clearCellContent(targetCell);

                // 复制段落和文本
                for (XWPFParagraph sourcePara : sourceCell.getParagraphs()) {
                    XWPFParagraph targetPara = targetCell.addParagraph();
                    copyParagraphContent(sourcePara, targetPara);
                }
            }
        }
    }


    /**
     * 清空单元格内容（不使用clear()方法）
     */
    private static void clearCellContent(XWPFTableCell cell) {
        // 移除所有段落，只保留一个空段落
//        while (cell.getParagraphs().size() > 1) {
//            cell.removeParagraph(1);
//        }
        while (cell.getParagraphs().size() > 0) {
            cell.removeParagraph(0);
        }

        // 清空保留的段落
//        XWPFParagraph para = cell.getParagraphs().get(0);
//        while (para.getRuns().size() > 0) {
//            para.removeRun(0);
//        }
//
//        // 添加一个空运行以维持段落结构
//        para.createRun();
    }
    /**
     * 复制段落内容
     */
    private static void copyParagraphContent(XWPFParagraph sourcePara, XWPFParagraph targetPara) {
        // 复制段落属性
        CTPPr sourcePPr = sourcePara.getCTP().getPPr();
        if (sourcePPr != null) {
            targetPara.getCTP().setPPr((CTPPr) sourcePPr.copy());
        }

        // 复制运行元素
//        targetPara.getRuns().clear();
        for (XWPFRun sourceRun : sourcePara.getRuns()) {
            XWPFRun targetRun = targetPara.createRun();

            // 复制运行属性
            CTRPr sourceRPr = sourceRun.getCTR().getRPr();
            if (sourceRPr != null) {
                targetRun.getCTR().setRPr((CTRPr) sourceRPr.copy());
            }
            // 复制文本
            targetRun.setText(sourceRun.getText(0), 0);
        }
    }

    public static void changeWordTable(XWPFDocument document,int sourceTableIndex, BusinessProcessing businessProcessing, Map<String, Object> parameters) throws OptimusException {
        // 获取表格
        XWPFTable sourceTable = document.getTables().get(sourceTableIndex);
        // 这个是业务处理（每次调用的业务各不相同）
        // 根据策略执行相应的业务逻辑
        if (businessProcessing != null) {
            parameters.put("document",document);
            boolean process = businessProcessing.process(sourceTable, parameters);
//            if (!process){
//                sourceTable.createRow();
//            }
        }
        // 合并单元格
//            TableCellsMerger.mergeSameCells(newTable);
    }


    public static void replaceWordText(XWPFDocument document,String placeholder, String replacement) {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceTextInParagraph(paragraph, placeholder, replacement);
        }

        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceTextInParagraph(paragraph, placeholder, replacement);
                    }
                }
            }
        }
    }


    private static void replaceTextInParagraph(XWPFParagraph paragraph, String placeholder, String replacement) {
        String text = paragraph.getText();
        if (text.contains(placeholder)) {
            text = text.replace(placeholder, replacement);
            setTextToParagraph(paragraph, text);
        }
    }

    private static void setTextToParagraph(XWPFParagraph paragraph, String text) {
        if (paragraph.getRuns().isEmpty()) {
            paragraph.createRun().setText(text);
            return;
        }

        XWPFRun firstRun = paragraph.getRuns().get(0);
        firstRun.setText(text, 0);

        for (int i = paragraph.getRuns().size() - 1; i > 0; i--) {
            paragraph.removeRun(i);
        }
    }


    public static void replaceImage(XWPFDocument document,String placeholder, String imagePath, int width, int height) throws IOException, InvalidFormatException {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String text = paragraph.getText();
            if (text.contains(placeholder)) {
                List<XWPFRun> runs = paragraph.getRuns();
                if (runs != null && runs.size() > 0) {
                    // 清除当前占位符文本 这里得循环删除，
                    for (int i = 0; i < runs.size(); i++) {
                        paragraph.removeRun(0);
                        i--;
                    }
                    XWPFRun run = paragraph.createRun();
                    // 添加图片
                    try (FileInputStream fis = new FileInputStream(imagePath)) {
                        run.addPicture(fis, XWPFDocument.PICTURE_TYPE_PNG,
                                "imagePath", Units.toEMU(width), Units.toEMU(height));
                    }
                }
            }
        }
    }
    /**
     * 将 XWPFDocument 保存到指定路径（封装底层流操作）
     * @param doc 待保存的文档对象
     * @param outputPath 输出文件路径（.docx）
     * @throws IOException 文件写入失败时抛出
     */
    public static void saveDocument(XWPFDocument doc, String outputPath) throws IOException {
        if (doc == null || outputPath == null || outputPath.trim().isEmpty()) {
            throw new IllegalArgumentException("文档或输出路径不能为空");
        }
        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            doc.write(fos); // 直接调用 XWPFDocument 的 write 方法
        }finally {
            closeDocument(doc);
        }
    }


    /**
     * 安全关闭 XWPFDocument（避免空指针或重复关闭）
     * @param doc 待关闭的文档（可为 null）
     */
    public static void closeDocument(XWPFDocument doc) {
        if (doc != null) {
            try {
                doc.close();
            } catch (IOException e) {
                // 可记录日志或忽略（根据业务需求）
                e.printStackTrace();
            }
        }
    }



}
