package com.gwssi.focus.reporting.utils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.boot.SpringApplication;

import java.io.*;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;

public class WordTableStyleExample {
    public static void main(String[] args) throws Exception {

        String inputPath = "D:\\gwdata\\验厂模型汇报-九影（深圳）电器有限公司.docx";
        String outputPath = "D:\\gwdata\\output.docx";

        // 处理Word文档
        processWordDocument(inputPath, outputPath, 3); // 0表示第一个表格
        processWordDocument(inputPath, outputPath, 5, "建议：\n\r" +
                "1. 确认发票异常原因\n\r" +
                "\t•\t核查发票号、发票代码、开票日期是否填写有误；\n\r" +
                "\t•\t与工厂确认是否为作废发票或测试用票误提交；\n\r" +
                "\t•\t如为重复票据，需查清是否属同一合同或供应商多开\n\r" +
                "统分析\n" +
                "• 项目二：需求设计\n" +
                "• 项目三：开发实现\n");
        processWordDocument(inputPath, outputPath);
    }

    /**
     * 处理Word文档中的指定表格
     * @param inputPath  输入文件路径
     * @param outputPath 输出文件路径
     * @param tableIndex 要处理的表格索引（从0开始）
     */
    public static void processWordDocument(String inputPath, String outputPath, int tableIndex) throws Exception {
        try (InputStream is = new FileInputStream(inputPath);
             XWPFDocument document = new XWPFDocument(is)) {

            // 获取指定表格
            XWPFTable targetTable = document.getTables().get(tableIndex);

            // 设置表格字体为小五（10.5磅）
            setTableFontSize(targetTable, 10.5);

            // 保存修改后的文档
            try (OutputStream os = new FileOutputStream(outputPath)) {
                document.write(os);
            }

        } catch (IOException e) {
            throw new RuntimeException("处理Word文档时发生错误", e);
        }
    }

    /**
     * 设置表格所有单元格的字体大小
     * @param table    目标表格
     * @param fontSize 字体大小（单位：磅）
     */
    private static void setTableFontSize(XWPFTable table, double fontSize) {
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    for (XWPFRun run : paragraph.getRuns()) {
                        // 设置字体大小（注意：POI中字号需要转换为TWIPS单位）
                        run.setFontSize((int) (9)); // 1磅 = 20 TWIPS

                        // 同时设置中文字体（可选）
//                        run.setFontFamily("宋体");
                    }
                }
            }
        }
    }

    /**
     * 处理Word文档
     * @param inputPath  输入文件路径
     * @param outputPath 输出文件路径
     * @param tableIndex 要删除的表格索引（从0开始）
     * @param deleteText 要删除的段落包含的文字（null表示不删除段落）
     */
    public static void processWordDocument(String inputPath, String outputPath,
                                           int tableIndex, String deleteText) throws Exception {
        try (InputStream is = new FileInputStream(inputPath);
             XWPFDocument document = new XWPFDocument(is)) {

            // 先删除表格（索引操作需要先执行）
            if (tableIndex >= 0 && tableIndex < document.getTables().size()) {
                deleteTableByIndex(document, tableIndex);
                System.out.println("已删除第" + (tableIndex+1) + "个表格");
            }

            // 再删除段落（需要在表格删除后操作，避免索引变化）
            if (deleteText != null && !deleteText.isEmpty()) {
                deleteParagraphByText(document, deleteText);
                System.out.println("已删除包含'" + deleteText + "'的段落");
            }

            // 保存修改后的文档
            try (OutputStream os = new FileOutputStream(outputPath)) {
                document.write(os);
            }

        } catch (IOException e) {
            throw new RuntimeException("处理Word文档时发生错误", e);
        }
    }

    /**
     * 根据索引删除表格
     * @param document  文档对象
     * @param tableIndex 表格索引（从0开始）
     */
    private static void deleteTableByIndex(XWPFDocument document, int tableIndex) {
        List<XWPFTable> tables = document.getTables();
        if (tableIndex < tables.size()) {
            XWPFTable targetTable = tables.get(tableIndex);
            int tablePosition = document.getPosOfTable(targetTable);
            document.removeBodyElement(tablePosition);
        }
    }

    /**
     * 根据文字内容删除段落
     * @param document   文档对象
     * @param deleteText 要删除的段落包含的文字
     */
    private static void deleteParagraphByText(XWPFDocument document, String deleteText) {
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = paragraphs.size() - 1; i >= 0; i--) { // 倒序删除防止索引变化
            XWPFParagraph paragraph = paragraphs.get(i);
            System.out.println(paragraph.getText());
            System.out.println("-----------------------------------------------------------------------------------");
            if (paragraph.getText().contains(deleteText)) {
                int paragraphPosition = document.getPosOfParagraph(paragraph);
                document.removeBodyElement(paragraphPosition);
                System.out.println("删除段落内容：" + paragraph.getText());
            }
        }
    }

    /**
     * 在Word文档中添加多段文字
     * @param inputPath  输入文件路径
     * @param outputPath 输出文件路径
     */
    public static void processWordDocument(String inputPath, String outputPath) throws Exception {
        try (InputStream is = new FileInputStream(inputPath);
             XWPFDocument document = new XWPFDocument(is)) {

            // 添加第一个普通段落
            addParagraph(document,
                    "建议：\n\r" +
                            "1. 确认发票异常原因\n\r" +
                            "\t•\t核查发票号、发票代码、开票日期是否填写有误；\n\r" +
                            "\t•\t与工厂确认是否为作废发票或测试用票误提交；\n\r" +
                            "\t•\t如为重复票据，需查清是否属同一合同或供应商多开。\n\r" +
                            "\n\r" +
                            "2. 要求补充合法合规的发票\n\r" +
                            "\t•\t要求工厂提供相同交易的替代有效发票；\n\r" +
                            "\t•\t如无替代发票，需说明采购方式及支付路径，并补充合同、付款凭证、收料单等佐证材料；\n\r" +
                            "\t•\t对于“挂靠采购”或“集团代开”情形，要求提供委托协议及内部往来记录。\n\r" +
                            "\n\r" +
                            "3. 穿透调查相关供应商\n\r" +
                            "\t•\t对于异常发票对应的供应商，应进一步核查其：\n\r" +
                            "\t•\t是否正常经营（可结合天眼查、发票流分析）；\n\r" +
                            "\t•\t是否存在与工厂法人、股东、财务人员有关系交叉；\n\r" +
                            "\t•\t是否为新设立企业、小规模纳税人、高风险行业公司。\n\r" +
                            "\n\r" +
                            "4. 加强本次验厂处理意见\n\r" +
                            "\t•\t如异常发票金额较大、比例较高，建议：\n\r" +
                            "\t•\t验厂结果列为“不通过”或“需复审”；\n\r" +
                            "\t•\t标记为重点监控对象，限制退税服务资格；\n\r" +
                            "\t•\t如异常为少量、可修复，可临时列为“整改项”，要求限期补票。\n\r",
                    null,
                    12,
                    false);

            // 添加第二个带样式的段落
            addStyledParagraph(document,
                    "这是第二个段落，包含加粗和斜体文本。",
                    "微软雅黑",
                    14,
                    true,
                    true);

            // 添加第三个带项目符号的段落
            addBulletList(document,
                    Arrays.asList("项目一：系统分析", "项目二：需求设计", "项目三：开发实现"));

            // 保存修改后的文档
            try (OutputStream os = new FileOutputStream(outputPath)) {
                document.write(os);
            }

        } catch (IOException e) {
            throw new RuntimeException("处理Word文档时发生错误", e);
        }
    }

    /**
     * 添加普通段落
     * @param document   文档对象
     * @param text       段落文本
     * @param fontFamily 字体名称（null表示默认字体）
     * @param fontSize   字体大小（磅）
     * @param isBold     是否加粗
     */
    private static void addParagraph(XWPFDocument document, String text,
                                     String fontFamily, Integer fontSize,
                                     boolean isBold) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();

        run.setText(text);
        setParagraphStyle(run, fontFamily, fontSize, isBold);
        System.out.println("已添加段落：" + text);
    }

    /**
     * 添加带样式的段落
     * @param document    文档对象
     * @param text        段落文本
     * @param fontFamily  字体名称
     * @param fontSize    字体大小
     * @param isBold      是否加粗
     * @param isItalic    是否斜体
     */
    private static void addStyledParagraph(XWPFDocument document, String text,
                                           String fontFamily, Integer fontSize,
                                           boolean isBold, boolean isItalic) {
        XWPFParagraph paragraph = document.createParagraph();
        XWPFRun run = paragraph.createRun();

        run.setText(text);
        setParagraphStyle(run, fontFamily, fontSize, isBold, isItalic);
        System.out.println("已添加样式段落：" + text);
    }

    /**
     * 添加项目符号列表
     * @param document 文档对象
     * @param items    列表项集合
     */
    private static void addBulletList(XWPFDocument document, List<String> items) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setIndentationLeft(400); // 设置缩进

        // 创建项目符号
        BigInteger bulletNumber = BigInteger.valueOf(1);
        for (String item : items) {
            XWPFRun run = paragraph.createRun();
            run.setText("•"); // 使用项目符号
            run.setFontSize(12);

            XWPFRun textRun = paragraph.createRun();
            textRun.setText(" " + item);
            textRun.setFontFamily("宋体");
            textRun.setFontSize(12);
            textRun.addBreak();

            paragraph.setIndentationHanging(720); // 设置悬挂缩进
        }
        System.out.println("已添加项目列表：" + items);
    }

    /**
     * 设置段落样式
     * @param run       Run对象
     * @param fontFamily 字体名称
     * @param fontSize   字体大小
     * @param isBold     是否加粗
     * @param isItalic   是否斜体（可选参数）
     */
    private static void setParagraphStyle(XWPFRun run, String fontFamily,
                                          Integer fontSize, boolean isBold,
                                          boolean... isItalic) {
        if (fontFamily != null) {
            run.setFontFamily(fontFamily);
        }
        if (fontSize != null) {
            run.setFontSize(fontSize);
        }
        run.setBold(isBold);
        if (isItalic.length > 0 && isItalic[0]) {
            run.setItalic(true);
        }
    }


    /**
     * 主处理方法
     * @param inputPath  输入文件路径
     * @param outputPath 输出文件路径
     * @param position   插入位置枚举
     */
    public void processWordDocument(String inputPath, String outputPath,
                                    InsertPosition position) throws Exception {
        try (InputStream is = new FileInputStream(inputPath);
             XWPFDocument document = new XWPFDocument(is)) {

            // 根据不同策略获取插入位置
            int insertIndex = getInsertIndex(document, position);

            // 创建要插入的多段落内容
            List<String> paragraphs = Arrays.asList(
                    "这是插入的第一个段落，包含**加粗**和*斜体*文本。",
                    "这是第二个段落，包含项目符号：",
                    "  • 项目一",
                    "  • 项目二",
                    "这是最后一个段落，包含超链接：https://www.example.com"
            );

            // 在指定位置插入段落
            insertParagraphsAt(document, insertIndex, paragraphs);

            // 保存修改后的文档
            try (OutputStream os = new FileOutputStream(outputPath)) {
                document.write(os);
            }

        } catch (IOException e) {
            throw new RuntimeException("处理Word文档时发生错误", e);
        }
    }

    /**
     * 根据插入策略获取目标位置索引
     * @param document 文档对象
     * @param position 插入位置枚举
     * @return 目标段落索引
     */
    private int getInsertIndex(XWPFDocument document, InsertPosition position) {
        switch (position) {
            case BEGINNING:
                return 0;
            case END:
                return document.getParagraphs().size();
            case AFTER_THIRD_PARAGRAPH:
                return Math.min(3, document.getParagraphs().size() - 1);
            default:
                throw new IllegalArgumentException("无效的插入位置");
        }
    }

    /**
     * 在指定位置插入多个段落
     * @param document   文档对象
     * @param insertIndex 目标位置索引
     * @param paragraphs  要插入的段落列表
     */
    private void insertParagraphsAt(XWPFDocument document, int insertIndex,
                                    List<String> paragraphs) {
        // 插入位置保护（不超过当前段落数量）
        insertIndex = Math.min(insertIndex, document.getParagraphs().size());

        // 批量插入段落
        for (int i = paragraphs.size() - 1; i >= 0; i--) {
//            XWPFParagraph newParagraph = document.insertNewParagraph(insertIndex + 1);
//            XWPFRun run = newParagraph.createRun();
//            run.setText(paragraphs.get(i));
//            applyParagraphFormatting(newParagraph, i % 2 == 0); // 交替设置段落格式
        }
    }

    /**
     * 应用段落格式
     * @param paragraph 段落对象
     * @param isEven    是否偶数段落（用于区分格式）
     */
    private void applyParagraphFormatting(XWPFParagraph paragraph, boolean isEven) {
        // 基础格式设置
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        paragraph.setSpacingAfter(200); // 段后间距1行（200TWIPS）

//        // 交替样式设置
//        if (isEven) {
//            paragraph.setIndentationLeft(400); // 左缩进4字符
//            run.setFontFamily("宋体");
//        } else {
//            paragraph.setIndentationFirstLine(200); // 首行缩进2字符
//            run.setFontFamily("微软雅黑");
//        }
    }

    /**
     * 插入位置枚举
     */
    enum InsertPosition {
        BEGINNING,    // 文档开头
        END,          // 文档结尾
        AFTER_THIRD_PARAGRAPH  // 第三个段落后
    }

}