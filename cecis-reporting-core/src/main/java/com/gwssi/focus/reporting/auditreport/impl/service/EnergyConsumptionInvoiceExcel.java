package com.gwssi.focus.reporting.auditreport.impl.service;

import com.gwssi.focus.reporting.auditreport.impl.BusinessProcessing;
import com.gwssi.focus.reporting.utils.BigDecimalUtils;
import com.gwssi.focus.reporting.utils.StringHandleUtils;
import com.gwssi.focus.reporting.utils.WordProcessorUtil;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBookmark;
import org.springframework.stereotype.Component;

import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/21 16:31
 * @Description: 能耗-验伪不通过的发票明细
 */
@Component
public class EnergyConsumptionInvoiceExcel implements BusinessProcessing {


    @Override
    public boolean process(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        IPersistenceDAO daoODS = DAOManager.getPersistenceDAO("dataSource_DB_ODS");
        SqlHelper sql = new SqlHelper();
        sql.append("select t.*,TO_CHAR(invoice_date, 'YYYY-MM-DD') as invoice_date_str from ods_input_invoice t where purchaser_name = ? and (goods_taxable_services like '%电费%' or goods_taxable_services like '%电服务费%' or goods_taxable_services like '%水费%' or goods_taxable_services like '%水服务费%') and verification_result = 1",parameters.get("company"));
        List<Map> maps = daoODS.queryForList(sql.getSql(), sql.getParamList());
//        if (maps.isEmpty()){
//            return false;
//        }
        BigDecimal total = BigDecimal.ZERO;
        for (int i = 0; i < maps.size(); i++) {
            XWPFTableRow row = WordProcessorUtil.createRow(table);
            row.getCell(0).setText(String.valueOf(i+1));
            row.getCell(1).setText(String.valueOf(maps.get(i).get("invoiceCode")));
            row.getCell(2).setText(String.valueOf(maps.get(i).get("invoiceNumber")));
            row.getCell(3).setText(String.valueOf(maps.get(i).get("digitalTicketNumber")));
            row.getCell(4).setText(String.valueOf(maps.get(i).get("sellerIdentificationNumber")));
            row.getCell(5).setText(String.valueOf(maps.get(i).get("sellerName")));
            row.getCell(6).setText(String.valueOf(maps.get(i).get("purchaserIdentificationNumber")));
            row.getCell(7).setText(String.valueOf(maps.get(i).get("purchaserName")));
            row.getCell(8).setText(String.valueOf(maps.get(i).get("invoiceDateStr")));
            row.getCell(9).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("amount"))));
            row.getCell(10).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("taxAmount"))));
            row.getCell(11).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("totalAmountIncludingTax"))));
            row.getCell(12).setText(String.valueOf(maps.get(i).get("invoiceSource")));
            row.getCell(13).setText(String.valueOf(maps.get(i).get("invoiceType")));
            row.getCell(14).setText(String.valueOf(maps.get(i).get("invoiceStatus")));
            row.getCell(15).setText(String.valueOf(maps.get(i).get("invoiceRiskLevel")));
            row.getCell(16).setText(String.valueOf(maps.get(i).get("drawer")));
            row.getCell(17).setText(String.valueOf(maps.get(i).get("remarks")));
            if (null != maps.get(i).get("totalAmountIncludingTax")){
                total = BigDecimalUtils.safeAdd(total,new BigDecimal(String.valueOf(maps.get(i).get("totalAmountIncludingTax"))));
            }
        }
//        WordProcessorUtil processor = (WordProcessorUtil)parameters.get("processor");
//        processor.replaceText("${energyConsumptionAmountSum}",total.toString());
//        if (table.getRows().size()<=1){
//            deleteTableByIndex((XWPFDocument)parameters.get("document"),5);
//        }
//        deleteParagraphByText((XWPFDocument)parameters.get("document"),"验伪不通过的发票明细：");


        // 示例1：删除所有空白页
//        processWordDocument("inputPath", "outputPath", OperationType.DELETE_BLANK_PAGES);

        // 示例2：删除第5页（基于分页符定位）
//        try {
//            processWordDocument("D:\\gwdata\\OUT\\验厂模型汇报-0603-九影（深圳）电器有限公司.docx", "D:\\gwdata\\OUT\\验厂模型汇报-0603-九影（深圳）电器有限公司123.docx", OperationType.DELETE_PAGE, 7);
//        }catch (Exception e){
//            e.printStackTrace();
//        }
        return true;
    }

    private static void deleteTableByIndex(XWPFDocument document, int tableIndex) {
        List<XWPFTable> tables = document.getTables();
        if (tableIndex < tables.size()) {
            XWPFTable targetTable = tables.get(tableIndex);
            int tablePosition = document.getPosOfTable(targetTable);
            document.removeBodyElement(tablePosition);
        }
    }

    private static void deleteParagraphByText(XWPFDocument document, String deleteText) {
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (int i = paragraphs.size() - 1; i >= 0; i--) { // 倒序删除防止索引变化
            XWPFParagraph paragraph = paragraphs.get(i);
            System.out.println(paragraph.getText());
            System.out.println("-----------------------------------------------------------------------------------");
            if (paragraph.getText().contains(deleteText)) {
                int paragraphPosition = document.getPosOfParagraph(paragraph);
                document.removeBodyElement(paragraphPosition);
//                System.out.println("删除段落内容：" + paragraph.getText());
            }
        }
    }
    /**
     * 主处理方法
     */
    public void processWordDocument(String inputPath, String outputPath,
                                    OperationType operation, Integer pageNumber) throws Exception {
        try (InputStream is = new FileInputStream(inputPath);
             XWPFDocument document = new XWPFDocument(is)) {

            switch (operation) {
                case DELETE_BLANK_PAGES:
                    deleteBlankPages(document);
                    break;
                case DELETE_PAGE:
                    if (pageNumber == null || pageNumber < 1) {
                        throw new IllegalArgumentException("无效的页码参数");
                    }
                    deleteSpecificPageByBookmark(document, pageNumber);
                    break;
            }

            // 保存修改后的文档
            try (OutputStream os = new FileOutputStream(outputPath)) {
                document.write(os);
            }

        } catch (IOException e) {
            throw new RuntimeException("处理Word文档时发生错误", e);
        }
    }

    /**
     * 删除所有空白页（核心逻辑：判断段落是否为空）
     */
    private void deleteBlankPages(XWPFDocument document) {
        List<Integer> blankParagraphIndices = new ArrayList<>();

        // 遍历所有段落，收集空段落索引
        for (int i = 0; i < document.getParagraphs().size(); i++) {
            XWPFParagraph para = document.getParagraphs().get(i);
            if (isBlankParagraph(para)) {
                blankParagraphIndices.add(i);
            }
        }

        // 倒序删除空段落（防止索引错乱）
        for (int i = blankParagraphIndices.size() - 1; i >= 0; i--) {
            int index = blankParagraphIndices.get(i);
            document.removeBodyElement(index);
        }
    }

    /**
     * 通过书签定位删除指定页码（兼容新版POI）
     */
    private void deleteSpecificPageByBookmark(XWPFDocument document, int pageNumber) {
        // 步骤1：在第pageNumber页末尾插入临时书签
        String startBookmarkName = "START_PAGE_" + pageNumber;
        String endBookmarkName = "END_PAGE_" + pageNumber;

        // 插入起始书签（第pageNumber页开头）
        XWPFParagraph startPara = document.createParagraph();
        XWPFRun startRun = startPara.createRun();
        startRun.setText("TEMP_START");
        CTBookmark startBookmark = startPara.getCTP().addNewBookmarkStart();
        startBookmark.setName(startBookmarkName);
        startBookmark.setId(BigInteger.valueOf(1));

        // 插入结束书签（第pageNumber页末尾）
        XWPFParagraph endPara = document.createParagraph();
        XWPFRun endRun = endPara.createRun();
        endRun.setText("TEMP_END");
        CTBookmark endBookmark = endPara.getCTP().addNewBookmarkStart();
        endBookmark.setName(endBookmarkName);
        endBookmark.setId(BigInteger.valueOf(2));

        // 步骤2：获取书签范围并删除内容
        // 注意：实际需通过XWPFDocument的getBookmark方法获取书签位置（此处简化逻辑）
        // 由于POI不直接支持页码，需手动调整书签位置或使用第三方库（如Apache POI扩展）

        // 替代方案：删除最后N页内容（假设每页约500字符）
        int charsPerPage = 5000; // 根据实际文档调整
        int totalCharsToDelete = charsPerPage * (pageNumber - 1);
        int currentCharCount = 0;

        for (int i = document.getParagraphs().size() - 1; i >= 0; i--) {
            XWPFParagraph para = document.getParagraphs().get(i);
            currentCharCount += para.getText().length();
            if (currentCharCount >= totalCharsToDelete) {
                document.removeBodyElement(i);
            }
        }
    }

    /**
     * 判断段落是否为空（核心逻辑）
     */
    private boolean isBlankParagraph(XWPFParagraph para) {
        // 条件1：段落文本为空（去除首尾空格）
        String text = para.getText().trim();
        if (!text.isEmpty()) return false;

        // 条件2：段落无运行（runs）或仅包含空运行
        List<XWPFRun> runs = para.getRuns();
        if (runs == null || runs.isEmpty()) return true;

        // 条件3：所有运行均无文本内容
        for (XWPFRun run : runs) {
            if (!run.getText(0).trim().isEmpty()) {
                return false;
            }
        }
        return true;
    }


    enum OperationType {
        DELETE_BLANK_PAGES,
        DELETE_PAGE
    }

}