package com.gwssi.focus.reporting.auditreport.impl.service;

import com.gwssi.focus.reporting.auditreport.impl.BusinessProcessing;
import com.gwssi.focus.reporting.utils.DocumentOperator;
import com.gwssi.focus.reporting.utils.StringHandleUtils;
import com.gwssi.focus.reporting.utils.WordProcessorUtil;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/21 16:31
 * @Description: 主要产品进项结构化分析
 */
@Component
public class InputProductsAnalyseExcel implements BusinessProcessing {

    public static final Map<String, String> SUGGESTION_MAP;
    public static final String SUGGESTION_STRING = "建议进一步核查以下事项：";

    static {
        SUGGESTION_MAP = new HashMap();
        SUGGESTION_MAP.put("1","\t•\t要求工厂说明缺失核心物料的采购原因，是自有库存消耗，还是委外加工、代工生产；\n" +
                "\t•\t调取ERP系统物料采购记录、库存进出记录，核查是否存在非发票流入的原料；\n" +
                "\t•\t若企业声称由外协或集团内企业统一采购，必须提供《委托加工协议》《入库单》等支撑文件；\n" +
                "\t•\t如确实无采购也无委托痕迹，提示风险较高，建议评定为“不通过”\t\n" +
                "•\t可延伸审查产成品库存是否异常增长，防止通过虚构库存掩盖无采购实情。");
        SUGGESTION_MAP.put("2","\t•\t逐项核查偏离比例较大的产品原材料进项记录，确认是否确实采用替代物料，如有需提供内部BOM或产品工艺单；\n" +
                "\t•\t抽查偏离幅度较大的原料采购发票是否真实，配套合同、对账单、入库单是否一致；\n" +
                "\t•\t若偏离为高估进项，需警惕是否存在代开发票行为（如供应商无产能、成立不久）；\n" +
                "\t•\t若偏离为低估进项，需查明是否存在无票采购、现金采购或委外未入账行为；\n" +
                "\t•\t建议重点穿透审查前10大供应商，是否为小微企业、是否存在人员/资金/地址重合。");
    }

    @Override
    public boolean process(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        IPersistenceDAO daoDWS = DAOManager.getPersistenceDAO("dataSource_DB_ADS");
        SqlHelper sql = new SqlHelper();
        sql.append("select * from ads_purchase_sale_matching t where usc_code = ? ",parameters.get("code"));
        List<Map> maps = daoDWS.queryForList(sql.getSql(), sql.getParamList());

        if (maps.isEmpty()){
            return false;
        }
        XWPFDocument document = (XWPFDocument)parameters.get("document");
//        String placeholder = (String)parameters.get("placeholder");
        String placeholder = "${inputProductsAnalyseSuggestion}";
        Map<String, String> stringStringMap = keyValue("ads_purchase_sale_matching");
        List<String> typeList = new ArrayList<>();
        for (int i = 0; i < maps.size(); i++) {
            XWPFTableRow row = WordProcessorUtil.createRow(table);
            row.getCell(0).setText(String.valueOf(i+1));
            row.getCell(1).setText((String)maps.get(i).get("categoryName"));
            row.getCell(2).setText((String)maps.get(i).get("itemName"));
            row.getCell(3).setText(null == maps.get(i).get("industryBegin")?"":StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("industryBegin")))+"-"+StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("industryEnd"))));
            row.getCell(4).setText(null == maps.get(i).get("actual")?"":formatDecimal(String.valueOf(maps.get(i).get("actual"))));


            String promptContent = (String)maps.get(i).get("promptContent");
            if (StringUtils.isBlank(promptContent)){
                row.getCell(5).setText("");
            }else {
                String value = "";
                String[] split = promptContent.split(",");
                for (int j = 0; j < split.length; j++) {
                    if (!typeList.contains(split[j])){
                        typeList.add(split[j]);
                    }
                    value+= stringStringMap.get(split[j]);
                    value+= "；";
                }
                row.getCell(5).setText(value);
            }
        }
        if (typeList.isEmpty()){
            DocumentOperator.deleteParagraphByText(document,placeholder);
        }else {
            List<String> list = new ArrayList<>();
            list.add(SUGGESTION_STRING);
            for (int i = 0; i < typeList.size(); i++) {
                list.addAll(Arrays.asList(SUGGESTION_MAP.get(typeList.get(i)).split("\n")));
            }
            DocumentOperator.replacePlaceholders(document,placeholder,list);
        }
        return true;
    }


    public static String formatDecimal(String input) {
        try {
            double number = Double.parseDouble(input);
            return String.format("%.2f", number);
        } catch (NumberFormatException e) {
            return input;
        }
    }

    private Map<String,String> keyValue(String dataTable) throws OptimusException {
        IPersistenceDAO daoDWD = DAOManager.getPersistenceDAO("dataSource_DB_ADS");
        SqlHelper sql = new SqlHelper();
        sql.append("select * from ADS_PROMPT_CONTENT where data_table = ? ",dataTable);
        Map<String, String> collect = daoDWD.queryForList(sql.getSql(), sql.getParamList()).stream().collect(Collectors.toMap(
                // 从每个Map中提取"id"字段作为键
                map -> (String) map.get("encode"),
                // 值就是Map本身
                map -> (String) map.get("enname"),
                // 处理键冲突：保留新值
                (existing, replacement) -> replacement
        ));
        return collect;
    }
}