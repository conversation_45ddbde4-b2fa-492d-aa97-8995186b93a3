package com.gwssi.focus.reporting.utils;

import com.gwssi.focus.reporting.auditreport.impl.BusinessProcessing;
import com.gwssi.optimus.core.exception.OptimusException;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.util.Units;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.drawingml.x2006.main.CTGraphicalObjectData;
import org.openxmlformats.schemas.drawingml.x2006.main.CTRegularTextRun;
import org.openxmlformats.schemas.drawingml.x2006.main.CTTextBody;
import org.openxmlformats.schemas.drawingml.x2006.picture.CTPicture;
import org.openxmlformats.schemas.drawingml.x2006.wordprocessingDrawing.CTInline;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigInteger;
import java.util.*;

@Component
public class WordProcessorUtil {
    private XWPFDocument document;
    private String templatePath;


    // 初始化方法
    public void init(String templatePath) throws IOException {
        if (Objects.equals(this.templatePath, templatePath) && document != null) {
            return; // 已使用相同路径初始化，无需重复操作
        }

        close(); // 关闭现有文档
        this.templatePath = templatePath;
        try (FileInputStream fis = new FileInputStream(templatePath)) {
            this.document = new XWPFDocument(fis);
        }
    }


    public void save(String outputPath) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            document.write(fos);
        }
    }

    public void close() throws IOException {
        if (document != null) {
            document.close();
            document = null;
        }
    }


    public void replaceText(String placeholder, String replacement) {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceTextInParagraph(paragraph, placeholder, replacement);
        }

        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceTextInParagraph(paragraph, placeholder, replacement);
                    }
                }
            }
        }
//        for (XWPFParagraph paragraph : document.getParagraphs()) {
//            for (XWPFRun run : paragraph.getRuns()) {
//                // 获取内联形状
//                for (CTR inline : run.getCTR().getInlineList()) {
//                    CTInline ctInline = inline.getInline();
//                    if (ctInline != null && ctInline.getInlineType() == STInlineType.OLE_OBJECT) {
//                        // 修改艺术字内容·
////                        modifyWordArt(inline);
//                    }
//                }
//            }
//        }
    }



    public void insertPdfFirstPageAsImage(String placeholder, String pdfPath, int width, int height) throws IOException, InvalidFormatException {
        try (PDDocument pdfDoc = PDDocument.load(new File(pdfPath))) {
            // 渲染PDF第一页为图片
            PDFRenderer renderer = new PDFRenderer(pdfDoc);
            BufferedImage image = renderer.renderImageWithDPI(0, 200); // 300 DPI高清渲染

            // 将图片转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "png", baos);
            byte[] imageBytes = baos.toByteArray();
            // 在Word中查找占位符并替换为图片
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                String text = paragraph.getText();
                if (text.contains(placeholder)) {
                    List<XWPFRun> runs = paragraph.getRuns();
                    if (runs != null && runs.size() > 0) {
                        // 清除当前占位符文本 这里得循环删除，
                        // TODO 不知道为什么会把文本拆分成多块
                        for (int i = 0; i < runs.size(); i++) {
                            paragraph.removeRun(0);
                            i--;
                        }
                        XWPFRun run = paragraph.createRun();
                        run.addPicture(
                                new ByteArrayInputStream(imageBytes),
                                XWPFDocument.PICTURE_TYPE_PNG,
                                "pdf_first_page.png",
                                Units.toEMU(width),
                                Units.toEMU(height)
                        );
                    }
                    break;
                }
            }
        } catch (InvalidFormatException e) {
            throw new RuntimeException(e);
        }
    }




    private void replaceTextInParagraph(XWPFParagraph paragraph, String placeholder, String replacement) {
        String text = paragraph.getText();
        if (text.contains(placeholder)) {
            text = text.replace(placeholder, replacement);
            setTextToParagraph(paragraph, text);
        }
    }

    private void setTextToParagraph(XWPFParagraph paragraph, String text) {
        if (paragraph.getRuns().isEmpty()) {
            paragraph.createRun().setText(text);
            return;
        }

        XWPFRun firstRun = paragraph.getRuns().get(0);
        firstRun.setText(text, 0);

        for (int i = paragraph.getRuns().size() - 1; i > 0; i--) {
            paragraph.removeRun(i);
        }
    }

    public void replaceImage(String placeholder, String imagePath, int width, int height) throws IOException, InvalidFormatException {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String text = paragraph.getText();
            if (text.contains(placeholder)) {
                List<XWPFRun> runs = paragraph.getRuns();
                if (runs != null && runs.size() > 0) {
                    // 清除当前占位符文本 这里得循环删除，
                    // TODO 不知道为什么会把文本拆分成多块
                    for (int i = 0; i < runs.size(); i++) {
                        paragraph.removeRun(0);
                        i--;
                    }
                    XWPFRun run = paragraph.createRun();
                    // 添加图片
                    try (FileInputStream fis = new FileInputStream(imagePath)) {
                        run.addPicture(fis, XWPFDocument.PICTURE_TYPE_PNG,
                                "imagePath", Units.toEMU(width), Units.toEMU(height));
                    }
                }
            }
        }
    }


    public void replaceImage(String placeholder, InputStream fis, int width, int height) throws IOException, InvalidFormatException {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String text = paragraph.getText();
            if (text.contains(placeholder)) {
                List<XWPFRun> runs = paragraph.getRuns();
                if (runs != null && runs.size() > 0) {
                    // 清除当前占位符文本 这里得循环删除，
                    // TODO 不知道为什么会把文本拆分成多块
                    for (int i = 0; i < runs.size(); i++) {
                        paragraph.removeRun(0);
                        i--;
                    }
                    XWPFRun run = paragraph.createRun();
                    // 添加图片
                    run.addPicture(fis, XWPFDocument.PICTURE_TYPE_PNG,
                            "imagePath", Units.toEMU(width), Units.toEMU(height));

                }
            }
        }
    }


    public void replaceTable(String placeholder, List<List<String>> data) {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String text = paragraph.getText();
            if (text.contains(placeholder)) {
                XWPFTable table = paragraph.getDocument().insertNewTbl(paragraph.getCTP().newCursor());
                // 创建3行
                for (int rowIndex = 0; rowIndex < 3; rowIndex++) {
                    XWPFTableRow row = (rowIndex == 0) ? table.getRow(0) : table.createRow();

                    // 确保每行有3个单元格
                    for (int colIndex = 0; colIndex < 3; colIndex++) {
                        // 如果单元格不存在，创建它
                        if (row.getTableCells().size() <= colIndex) {
                            row.addNewTableCell();
                        }

                        // 获取并设置单元格内容
                        XWPFTableCell cell = row.getCell(colIndex);
                        if (cell != null) {
                            cell.setText("Row " + (rowIndex + 1) + ", Col " + (colIndex + 1));
                        }
                    }
                }
                setTextToParagraph(paragraph, text.replace(placeholder, ""));
                break;
            }
        }
    }
    public void replaceWordArtText(String placeholder, String replacement) {
        for (XWPFParagraph p : document.getParagraphs()) {
            for (XWPFRun run : p.getRuns()) {
                // 检查运行元素是否包含DrawingML对象（艺术字）
                CTR ctr = run.getCTR();
                if (ctr != null && ctr.getDrawingList() != null) {
                    for (CTDrawing drawing : ctr.getDrawingList()) {
                        processDrawingML(drawing, placeholder, replacement);
                    }
                }
            }
        }
    }

    private void processDrawingML(CTDrawing drawing, String placeholder, String replacement) {
        // 处理内联对象
        for (CTInline inline : drawing.getInlineList()) {
            if (inline.getGraphic() != null &&
                    inline.getGraphic().getGraphicData() != null) {

                // 获取图形数据
                CTGraphicalObjectData graphicData = inline.getGraphic().getGraphicData();

                // 检查是否包含文本体（艺术字）
                if (graphicData.getDomNode().getLocalName().equals("textBody")) {
                    // 使用XPath或直接遍历子元素查找文本
                    processTextBody(graphicData, placeholder, replacement);
                }
            }
        }
    }

    private void processTextBody(CTGraphicalObjectData graphicData, String placeholder, String replacement) {
        // 使用XPath查找所有文本节点
        org.w3c.dom.NodeList textNodes = graphicData.getDomNode().getChildNodes();

        for (int i = 0; i < textNodes.getLength(); i++) {
            org.w3c.dom.Node node = textNodes.item(i);

            // 处理文本段落
            if ("p".equals(node.getLocalName())) {
                // 遍历段落中的所有文本运行
                org.w3c.dom.NodeList runNodes = node.getChildNodes();
                for (int j = 0; j < runNodes.getLength(); j++) {
                    org.w3c.dom.Node runNode = runNodes.item(j);

                    // 处理文本运行
                    if ("r".equals(runNode.getLocalName())) {
                        // 查找文本内容
                        org.w3c.dom.NodeList textChildNodes = runNode.getChildNodes();
                        for (int k = 0; k < textChildNodes.getLength(); k++) {
                            org.w3c.dom.Node textNode = textChildNodes.item(k);

                            // 找到文本节点
                            if ("t".equals(textNode.getLocalName()) && textNode.getTextContent() != null) {
                                String text = textNode.getTextContent();
                                if (text.contains(placeholder)) {
                                    textNode.setTextContent(text.replace(placeholder, replacement));
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    public void replaceTable1(String placeholder, String data) {
        for (IBodyElement element : document.getBodyElements()) {
            if (element instanceof XWPFParagraph) {
                XWPFParagraph paragraph = (XWPFParagraph) element;
                for (XWPFRun run : paragraph.getRuns()) {
                    for (XWPFPicture pic : run.getEmbeddedPictures()) {
                        CTPicture ctPicture = pic.getCTPicture();
                        CTTextBody textBody = (CTTextBody) ctPicture.getNvPicPr().getCNvPr().getExtLst(); // 这里可能需要调整以匹配实际结构

                        // 如果是艺术字，通常会有文本主体
                        if (textBody != null) {
                            // 遍历文本体中的所有文本运行
                            for (CTRegularTextRun textRun : textBody.getPArray(0).getRList()) {
                                // 替换文本
                                String oldText = textRun.getT();
                                if (oldText.contains(placeholder)) {
                                    textRun.setT(oldText.replace(placeholder, data));
                                }
                            }
                        }
                    }
                }
            }
        }

    }




    public void insertPdfContent(String placeholder, String pdfPath) throws IOException {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String text = paragraph.getText();
            if (text.contains(placeholder)) {
                try (PDDocument pdf = PDDocument.load(new File(pdfPath))) {
                    PDFTextStripper stripper = new PDFTextStripper();
                    String pdfText = stripper.getText(pdf);
                    paragraph.removeRun(0);
                    XWPFRun run = paragraph.createRun();
                    run.setText(pdfText);
//                    setTextToParagraph(paragraph, text.replace(placeholder, ""));
                }
                break;
            }
        }
    }


public void importExcelTable(String placeholder, String excelPath, String sheetName) throws IOException {
    try (FileInputStream fis = new FileInputStream(excelPath);
         Workbook workbook = new XSSFWorkbook(fis)) {

        Sheet sheet = workbook.getSheet(sheetName);
        if (sheet == null) {
            return;
        }

        List<List<String>> data = new ArrayList<>();
        for (Row row : sheet) {
            List<String> rowData = new ArrayList<>();
            for (Cell cell : row) {
                switch (cell.getCellType()) {
                    case STRING:
                        rowData.add(cell.getStringCellValue());
                        break;
                    case NUMERIC:
                        rowData.add(String.valueOf(cell.getNumericCellValue()));
                        break;
                    case BOOLEAN:
                        rowData.add(String.valueOf(cell.getBooleanCellValue()));
                        break;
                    default:
                        rowData.add("");
                }
            }
            data.add(rowData);
        }

        if (!data.isEmpty()) {
            insertTableAfterPlaceholder(placeholder, data, data.get(0).size());
        }
    }
}
    public void insertTableAfterPlaceholder(String placeholder, List<List<String>> data, int numColumns) {
        for (int i = 0; i < document.getParagraphs().size(); i++) {
            XWPFParagraph p = document.getParagraphs().get(i);
            if (p.getText().contains(placeholder)) {
                XWPFTable table = document.createTable(data.size(), numColumns);

                for (int rowIdx = 0; rowIdx < data.size(); rowIdx++) {
                    List<String> rowData = data.get(rowIdx);
                    XWPFTableRow row = table.getRow(rowIdx);
                    for (int colIdx = 0; colIdx < Math.min(rowData.size(), numColumns); colIdx++) {
                        row.getCell(colIdx).setText(rowData.get(colIdx));
                    }
                }

                // 可选：设置表格样式
                setTableStyle(table);

                // 移除占位符
                p.removeRun(0);
                p.createRun().setText("");
                break;
            }
        }
    }
    private void setTableStyle(XWPFTable table) {
        // 设置表格边框
        CTTblBorders borders = table.getCTTbl().getTblPr().addNewTblBorders();
        // 设置其他表格样式...
    }

    public void changeWordTable(int sourceTableIndex, BusinessProcessing businessProcessing,Map<String, Object> parameters) throws OptimusException {
            // 获取表格
        XWPFTable sourceTable = document.getTables().get(sourceTableIndex);
        // 这个是业务处理（每次调用的业务各不相同）
        // 根据策略执行相应的业务逻辑
        if (businessProcessing != null) {
            parameters.put("document",document);
            boolean process = businessProcessing.process(sourceTable, parameters);
//            if (!process){
//                sourceTable.createRow();
//            }
        }
        // 合并单元格
//            TableCellsMerger.mergeSameCells(newTable);
    }



    public void importWordTable(String placeholder, String wordPath, int sourceTableIndex, BusinessProcessing businessProcessing,Map<String, Object> parameters) {
        try (FileInputStream sourceFis = new FileInputStream(wordPath);
             XWPFDocument sourceDoc = new XWPFDocument(sourceFis)) {

            // 获取源表格
            XWPFTable sourceTable = sourceDoc.getTables().get(sourceTableIndex);

            // 查找占位符段落
            XWPFParagraph placeholderPara = findParagraphByText(document, placeholder);
            if (placeholderPara == null) {
                throw new IllegalArgumentException("未找到占位符: " + placeholder);
            }
            // 创建新表格并复制内容
            XWPFTable newTable = placeholderPara.getDocument().insertNewTbl(placeholderPara.getCTP().newCursor());
            copyTableContent(sourceTable, newTable);

            // TODO 这个是业务处理（每次调用的业务各不相同）
            // 根据策略执行相应的业务逻辑
            if (businessProcessing != null) {
                boolean process = businessProcessing.process(newTable, parameters);
                if (!process){
                    newTable.createRow();
//                    int pos = document.getPosOfTable(newTable);
//                    document.removeBodyElement(pos);
//                    newTable = null;
                }
            }
            // 合并单元格
            TableCellsMerger.mergeSameCells(newTable);
            // 移除占位符文本

            clearParagraphText(placeholderPara);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (OptimusException e) {
            throw new RuntimeException(e);
        }
    }
    /**
     * 根据文本内容查找段落
     */
    private static XWPFParagraph findParagraphByText(XWPFDocument doc, String text) {
        for (XWPFParagraph para : doc.getParagraphs()) {
            if (para.getText().contains(text)) {
                return para;
            }
        }
        return null;
    }

    /**
     * 清除段落文本
     */
    private static void clearParagraphText(XWPFParagraph para) {
        for (int i = para.getRuns().size() - 1; i >= 0; i--) {
            para.removeRun(i);
        }
        para.createRun().setText(""); // 保留空运行以维持段落结构
    }
    /**
     * 复制表格内容（修正版）
     */
    private static void copyTableContent(XWPFTable sourceTable, XWPFTable targetTable) {
        // 复制表格属性
        CTTblPr sourceTblPr = sourceTable.getCTTbl().getTblPr();
        if (sourceTblPr != null) {
            targetTable.getCTTbl().setTblPr((CTTblPr) sourceTblPr.copy());
        }

        // 复制行和单元格
        for (int i = 0; i < sourceTable.getRows().size(); i++) {
            XWPFTableRow sourceRow = sourceTable.getRow(i);
            XWPFTableRow targetRow = (i == 0) ? targetTable.getRow(0) : targetTable.createRow();

            // 复制行属性
            CTTrPr sourceTrPr = sourceRow.getCtRow().getTrPr();
            if (sourceTrPr != null) {
                targetRow.getCtRow().setTrPr((CTTrPr) sourceTrPr.copy());
            }

            // 确保目标行有足够的单元格
            while (targetRow.getTableCells().size() < sourceRow.getTableCells().size()) {
                targetRow.addNewTableCell();
            }

            // 复制单元格内容和样式
            for (int j = 0; j < sourceRow.getTableCells().size(); j++) {
                XWPFTableCell sourceCell = sourceRow.getCell(j);
                XWPFTableCell targetCell = targetRow.getCell(j);

                // 复制单元格属性
                CTTcPr sourceTcPr = sourceCell.getCTTc().getTcPr();
                if (sourceTcPr != null) {
                    targetCell.getCTTc().setTcPr((CTTcPr) sourceTcPr.copy());
                }

                // 清空目标单元格（修正版）
                clearCellContent(targetCell);

                // 复制段落和文本
                for (XWPFParagraph sourcePara : sourceCell.getParagraphs()) {
                    XWPFParagraph targetPara = targetCell.addParagraph();
                    copyParagraphContent(sourcePara, targetPara);
                }
            }
        }
    }
    /**
     * 清空单元格内容（不使用clear()方法）
     */
    private static void clearCellContent(XWPFTableCell cell) {
        // 移除所有段落，只保留一个空段落
//        while (cell.getParagraphs().size() > 1) {
//            cell.removeParagraph(1);
//        }
        while (cell.getParagraphs().size() > 0) {
            cell.removeParagraph(0);
        }

        // 清空保留的段落
//        XWPFParagraph para = cell.getParagraphs().get(0);
//        while (para.getRuns().size() > 0) {
//            para.removeRun(0);
//        }
//
//        // 添加一个空运行以维持段落结构
//        para.createRun();
    }
    /**
     * 复制段落内容
     */
    private static void copyParagraphContent(XWPFParagraph sourcePara, XWPFParagraph targetPara) {
        // 复制段落属性
        CTPPr sourcePPr = sourcePara.getCTP().getPPr();
        if (sourcePPr != null) {
            targetPara.getCTP().setPPr((CTPPr) sourcePPr.copy());
        }

        // 复制运行元素
//        targetPara.getRuns().clear();
        for (XWPFRun sourceRun : sourcePara.getRuns()) {
            XWPFRun targetRun = targetPara.createRun();

            // 复制运行属性
            CTRPr sourceRPr = sourceRun.getCTR().getRPr();
            if (sourceRPr != null) {
                targetRun.getCTR().setRPr((CTRPr) sourceRPr.copy());
            }
            // 复制文本
            targetRun.setText(sourceRun.getText(0), 0);
        }
    }

    public static XWPFTableRow createRow(XWPFTable table) {
        XWPFTableRow row = table.createRow();
        ensureRowHasCells( row, table.getRow(0).getTableCells().size());
        return row;
    }

    /**
     * 确保行有足够的单元格
     */
    private static void ensureRowHasCells(XWPFTableRow row, int cellCount) {
        for (int i = 0; i < cellCount; i++) {
            // 获取单元格的段落
            List<XWPFParagraph> paragraphs = row.getCell(i).getParagraphs();
            for (int j = 0; j < paragraphs.size(); j++) {
                paragraphs.get(j).createRun().setFontSize(9);
            }
//            XWPFParagraph paragraph = row.getCell(i).getParagraphs().get(0);
//            // 创建文本运行并设置内容
//            XWPFRun run = paragraph.createRun();
//            // 设置字体大小（单位：磅）
//            run.setFontSize(9); // 小五对应的磅值是9
        }
    }

//    private static void setTableFontSizeOptimized(XWPFTable table, int fontSize) {
//        CTRPr rPr = CTRPr.Factory.newInstance();
//        rPr.addNewSz().setVal(BigInteger.valueOf(fontSize * 2)); // 注意：这里乘以2是Word内部单位转换
//
//        for (XWPFTableRow row : table.getRows()) {
//            for (XWPFTableCell cell : row.getTableCells()) {
//                for (XWPFParagraph paragraph : cell.getParagraphs()) {
//                    for (XWPFRun run : paragraph.getRuns()) {
//                        run.getCTR().setRPr(rPr); // 应用共享的字体设置
//                    }
//                }
//            }
//        }
//    }
}
