package com.gwssi.focus.reporting.auditreport.impl.service;

import com.gwssi.focus.reporting.auditreport.impl.BusinessProcessing;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTHMerge;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTVMerge;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2025/5/21 16:31
 * @Description: 验厂模型筛查结果展示
 */
@Component
public class ScreeningResultsExcel implements BusinessProcessing {

    @Override
    public boolean process(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        IPersistenceDAO daoADS = DAOManager.getPersistenceDAO("DB_ADS");
        SqlHelper sql = new SqlHelper().append("select r.*,i.PROMPT_INFORMATION,case when INDICATOR_VALUE > 0 then '1' else '0' end as INDICATOR_VALUE_STR" +
                " from ADS_FACTORY_INSPECTION_RESULTS r left join ADS_INDICATOR i on r.INDICATOR_NUMBER = i.INDICATOR_NUMBER " +
                " where r.name = ? and r.number = ? and r.indicator_hierarchy in (3,4,5) order by r.indicator_hierarchy,r.INDICATOR_NUMBER ", parameters.get("company"),parameters.get("number"));
        List<Map> maps = daoADS.queryForList(sql.getSql(), sql.getParamList());
        for (int i = 0; i < maps.size(); i++) {
            if (!"3".equals(String.valueOf(maps.get(i).get("indicatorHierarchy")))){
                continue;
            }
            for (int j = 0; j < maps.size(); j++) {
                if (maps.get(i).get("indicatorNumber").equals(maps.get(j).get("parentIndicatorNumber"))){
                    if ("0".equals(maps.get(i).get("indicatorValueStr"))){
                        XWPFTableRow row = table.createRow();
                        ensureRowHasCells(row, table.getRow(0).getTableCells().size());
                        row.getCell(0).setText(String.valueOf(maps.get(i).get("indicatorName")));
//                        refreshCell(row.getCell(0));
                        row.getCell(1).setText(String.valueOf(maps.get(j).get("indicatorName")));
                        if ("企业资信筛查指标".equals(String.valueOf(maps.get(j).get("indicatorName")))){
                            System.out.println();
                        }
                        row.getCell(2).setText("0".equals(maps.get(j).get("indicatorValueStr"))?"通过":"不通过");
                        row.getCell(3).setText("");
                    }else {
                        if ("0".equals(maps.get(j).get("indicatorValueStr"))){
                            XWPFTableRow row = table.createRow();
                            ensureRowHasCells(row, table.getRow(0).getTableCells().size());
                            row.getCell(0).setText(String.valueOf(maps.get(i).get("indicatorName")));
//                        refreshCell(row.getCell(0));
                            row.getCell(1).setText(String.valueOf(maps.get(j).get("indicatorName")));
                            if ("企业资信筛查指标".equals(String.valueOf(maps.get(j).get("indicatorName")))){
                                System.out.println();
                            }
                            row.getCell(2).setText("0".equals(maps.get(j).get("indicatorValueStr"))?"通过":"不通过");
                            row.getCell(3).setText("");
                        }else {
                            for (int k = 0; k < maps.size(); k++) {
                                if (maps.get(j).get("indicatorNumber").equals(maps.get(k).get("parentIndicatorNumber")) && !"".equals(maps.get(k).get("indicatorValueStr"))&& null!=maps.get(k).get("indicatorValueStr")){
                                    if (!"0".equals(maps.get(k).get("indicatorValueStr"))){
                                        XWPFTableRow row = table.createRow();
                                        ensureRowHasCells(row, table.getRow(0).getTableCells().size());
                                        row.getCell(0).setText(String.valueOf(maps.get(i).get("indicatorName")));
                                        row.getCell(1).setText(String.valueOf(maps.get(j).get("indicatorName")));
                                        if ("纳税合规性审查指标".equals(String.valueOf(maps.get(j).get("indicatorName")))){
                                            System.out.println();
                                        }
                                        row.getCell(2).setText("0".equals(maps.get(j).get("indicatorValueStr"))?"通过":"不通过");
                                        row.getCell(3).setText("0".equals(maps.get(j).get("indicatorValueStr"))?"":String.valueOf(maps.get(k).get("promptInformation")));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return true;
    }

    // 刷新单元格内容的工具方法
    private void refreshCell(XWPFTableCell cell) {
        if (!cell.getParagraphs().isEmpty()) {
            XWPFParagraph para = cell.getParagraphs().get(0);
            if (para.getRuns().isEmpty()) {
                para.createRun().setText(""); // 创建空运行（避免空段落问题）
            }
            XWPFRun run = para.getRuns().get(0);
            run.setText(run.getText(0), 0); // 触发内容更新
        }
    }

    public static void mergeSameCells(XWPFTable table) {
        int rowCount = table.getNumberOfRows();
        if (rowCount == 0) return;

        int colCount = table.getRow(0).getTableCells().size();
        boolean[][] mergedCells = new boolean[rowCount][colCount];

        for (int i = 0; i < rowCount; i++) {
            XWPFTableRow row = table.getRow(i);
            if (row == null) continue;

            for (int j = 0; j < colCount; j++) {
                if (mergedCells[i][j]) continue;

                XWPFTableCell currentCell = row.getCell(j);
                if (currentCell == null) continue;

                String content = currentCell.getText().trim();
                if (content.isEmpty()) continue;

                int maxCol = j;
                int maxRow = i;

                // 寻找最大合并范围
                boolean validRegion = true;
                // 横向扩展
                while (maxCol + 1 < colCount) {
                    XWPFTableCell rightCell = row.getCell(maxCol + 1);
                    if (rightCell == null || !rightCell.getText().trim().equals(content)) break;
                    maxCol++;
                }
                // 纵向扩展
                while (maxRow + 1 < rowCount) {
                    XWPFTableRow downRow = table.getRow(maxRow + 1);
                    if (downRow == null) break;
                    XWPFTableCell downCell = downRow.getCell(j);
                    if (downCell == null || !downCell.getText().trim().equals(content)) break;
                    maxRow++;
                }

                // 验证整个区域内容一致性
                validRegion = validateRegion(table, i, j, maxRow, maxCol, content);

                if (validRegion) {
                    mergeRegion(table, i, j, maxRow, maxCol);
                    markMerged(mergedCells, i, j, maxRow, maxCol);
                    j = maxCol; // 跳过已合并列
                } else {
                    // 尝试横向合并
                    if (maxCol > j) {
                        mergeHorizontal(table, i, j, maxCol);
                        markMerged(mergedCells, i, j, maxCol, j);
                        j = maxCol;
                    }
                    // 尝试纵向合并
                    if (maxRow > i) {
                        mergeVertical(table, i, j, maxRow);
                        markMerged(mergedCells, i, j, maxRow, j);
                        i = maxRow; // 跳过已合并行
                    }
                }
            }
        }
    }

    private static boolean validateRegion(XWPFTable table, int startRow, int startCol,
                                          int endRow, int endCol, String content) {
        for (int r = startRow; r <= endRow; r++) {
            XWPFTableRow row = table.getRow(r);
            if (row == null) return false;
            for (int c = startCol; c <= endCol; c++) {
                XWPFTableCell cell = row.getCell(c);
                if (cell == null || !cell.getText().trim().equals(content)) {
                    return false;
                }
            }
        }
        return true;
    }

    private static void mergeRegion(XWPFTable table, int startRow, int startCol,
                                    int endRow, int endCol) {
        for (int r = startRow; r <= endRow; r++) {
            XWPFTableRow row = table.getRow(r);
            if (row == null) continue;
            for (int c = startCol; c <= endCol; c++) {
                XWPFTableCell cell = row.getCell(c);
                if (cell == null) continue;
                CTTcPr tcPr = cell.getCTTc().getTcPr();
                if (tcPr == null) tcPr = cell.getCTTc().addNewTcPr();

                if (r == startRow && c == startCol) {
                    // 左上角单元格
                    CTVMerge vMerge = tcPr.addNewVMerge();
                    vMerge.setVal(STMerge.RESTART);
                    CTHMerge hMerge = tcPr.addNewHMerge();
                    hMerge.setVal(STMerge.RESTART);
                } else {
                    // 其他单元格
                    if (r == startRow) {
                        // 同行单元格
                        CTHMerge hMerge = tcPr.addNewHMerge();
                        hMerge.setVal(STMerge.CONTINUE);
                    }
                    if (c == startCol) {
                        // 同列单元格
                        CTVMerge vMerge = tcPr.addNewVMerge();
                        vMerge.setVal(STMerge.CONTINUE);
                    }
                }
            }
        }
    }

    private static void mergeHorizontal(XWPFTable table, int row, int startCol, int endCol) {
        XWPFTableCell firstCell = table.getRow(row).getCell(startCol);
        for (int c = startCol + 1; c <= endCol; c++) {
            XWPFTableCell cell = table.getRow(row).getCell(c);
            if (cell == null) continue;

            // 合并属性
            CTTcPr tcPr = firstCell.getCTTc().getTcPr();
            if (tcPr == null) tcPr = firstCell.getCTTc().addNewTcPr();

            CTHMerge hMerge = tcPr.addNewHMerge();
            hMerge.setVal(STMerge.RESTART);

            // 正确方式：清除被合并单元格的内容
            for (int i = cell.getParagraphs().size() - 1; i >= 0; i--) {
                cell.removeParagraph(i);
            }

            // 移动内容
            firstCell.getParagraphs().addAll(cell.getParagraphs());
        }
    }

    private static void mergeVertical(XWPFTable table, int col, int startRow, int endRow) {
        XWPFTableCell firstCell = table.getRow(startRow).getCell(col);
        for (int r = startRow + 1; r <= endRow; r++) {
            XWPFTableCell cell = table.getRow(r).getCell(col);
            if (cell == null) continue;

            // 合并属性
            CTTcPr tcPr = firstCell.getCTTc().getTcPr();
            if (tcPr == null) tcPr = firstCell.getCTTc().addNewTcPr();

            CTVMerge vMerge = tcPr.addNewVMerge();
            vMerge.setVal(STMerge.RESTART);

            // 正确方式：清除被合并单元格的内容
            for (int i = cell.getParagraphs().size() - 1; i >= 0; i--) {
                cell.removeParagraph(i);
            }

            // 移动内容
            firstCell.getParagraphs().addAll(cell.getParagraphs());
        }
    }

    private static void markMerged(boolean[][] mergedCells, int startRow, int startCol,
                                   int endRow, int endCol) {
        for (int r = startRow; r <= endRow; r++) {
            for (int c = startCol; c <= endCol; c++) {
                mergedCells[r][c] = true;
            }
        }
    }


    // 主转换方法
    public static List<IndicatorNode> convertToIndicatorNodes(List<Map<String, Object>> flatList) {
        List<IndicatorNode> resultList = new ArrayList<>();
        Map<String, IndicatorNode> nodeMap = new HashMap<>();

        // 第一遍：创建所有节点并存储到Map中
        for (Map<String, Object> row : flatList) {
            String number = (String) row.get("indicatorNumber");
            String name = (String) row.get("indicatorName");
            String hierarchy = (String) row.get("indicatorHierarchy");
            String indicatorValueStr = (String) row.get("indicatorValueStr");
            String promptInformation = (String) row.get("promptInformation");
            IndicatorNode node = new IndicatorNode(number, name, hierarchy,indicatorValueStr,promptInformation);
            nodeMap.put(number, node);
        }

        // 第二遍：递归构建并收集节点
        for (Map<String, Object> row : flatList) {
            String number = (String) row.get("indicatorNumber");
            String parentNumber = (String) row.get("parentIndicatorNumber");
            String hierarchy = (String) row.get("indicatorHierarchy");
            IndicatorNode node = nodeMap.get(number);
            if ("2".equals(hierarchy)) {
                // 根节点直接添加
                resultList.add(node);
            }

            // 递归添加子节点
            addChildrenRecursively(node, flatList, nodeMap);
        }

        return resultList;
    }

    // 递归添加子节点
    private static void addChildrenRecursively(
            IndicatorNode parent,
            List<Map<String, Object>> flatList,
            Map<String, IndicatorNode> nodeMap
    ) {
        for (Map<String, Object> row : flatList) {
            String parentNumber = (String) row.get("parentIndicatorNumber");
            if (parentNumber != null && parentNumber.equals(parent.indicatorNumber)) {
                String childNumber = (String) row.get("indicatorNumber");
                IndicatorNode child = nodeMap.get(childNumber);

                if (child != null) {
                    parent.children.add(child);
                    // 递归处理子节点的子节点
                    addChildrenRecursively(child, flatList, nodeMap);
                }
            }
        }
    }


    // 将平铺数据转换为树形结构
    public static Map<String, IndicatorNode> buildTree(List<Map<String, Object>> flatList) {
        Map<String, IndicatorNode> nodeMap = new HashMap<>();
        Map<String, IndicatorNode> rootNodes = new HashMap<>();

        // 创建所有节点
        for (Map<String, Object> row : flatList) {
            String indicatorNumber = (String) row.get("indicator_number");
            String indicatorName = (String) row.get("indicator_name");
            int hierarchy = (Integer) row.get("indicator_hierarchy");
//            IndicatorNode node = new IndicatorNode(indicatorNumber, indicatorName, hierarchy);
//            nodeMap.put(indicatorNumber, node);
        }

        // 构建父子关系
        for (Map<String, Object> row : flatList) {
            String indicatorNumber = (String) row.get("indicator_number");
            String parentNumber = (String) row.get("parent_indicator_number");
            IndicatorNode node = nodeMap.get(indicatorNumber);

            if (parentNumber == null || !nodeMap.containsKey(parentNumber)) {
                rootNodes.put(indicatorNumber, node);
            } else {
                IndicatorNode parent = nodeMap.get(parentNumber);
                parent.children.add(node);
            }
        }

        return rootNodes;
    }

    // 生成多级表格数据
    public static List<List<String>> generateTableData(Map<String, IndicatorNode> rootNodes, int maxLevel) {
        List<List<String>> tableData = new ArrayList<>();
        for (IndicatorNode root : rootNodes.values()) {
            List<String> currentPath = new ArrayList<>(Collections.nCopies(maxLevel, ""));
            generateTableRows(root, currentPath, 0, tableData, maxLevel);
        }
        return tableData;
    }

    // 递归生成表格行（支持任意层级）
    private static void generateTableRows(IndicatorNode node, List<String> currentPath, int currentLevel,
                                          List<List<String>> tableData, int maxLevel) {
        if (node == null || currentLevel >= maxLevel) return;

        // 更新当前路径
        currentPath.set(currentLevel, node.indicatorName);

        // 如果是叶子节点或已达到最大层级，添加当前路径到表格
        if (node.children.isEmpty() || currentLevel == maxLevel - 1) {
            tableData.add(new ArrayList<>(currentPath));
        } else {
            // 递归处理所有子节点
            for (IndicatorNode child : node.children) {
                generateTableRows(child, currentPath, currentLevel + 1, tableData, maxLevel);
            }
        }

        // 回溯，清除当前节点的影响
        currentPath.set(currentLevel, "");
    }




    /**
     * 确保行有足够的单元格
     */
    private static void ensureRowHasCells(XWPFTableRow row, int cellCount) {
        while (row.getTableCells().size() < cellCount) {
//            row.addNewTableCell();
            row.createCell();
        }
    }
}