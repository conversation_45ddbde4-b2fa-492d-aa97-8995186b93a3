package com.gwssi.focus.reporting.auditreport.impl.service;

import com.gwssi.focus.reporting.auditreport.impl.BusinessProcessing;
import com.gwssi.focus.reporting.utils.DocumentOperator;
import com.gwssi.focus.reporting.utils.StringHandleUtils;
import com.gwssi.focus.reporting.utils.WordProcessorUtil;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/21 16:31
 * @Description: 能耗指标与出货量合理性审查指标详情
 */
@Component
public class EnergyConsumptionAndShipmentsCensorExcel implements BusinessProcessing {
    public static final Map<String, String> SUGGESTION_MAP;
    public static final String SUGGESTION_STRING = "建议进一步核查以下事项：";

    static {
        SUGGESTION_MAP = new HashMap();
        SUGGESTION_MAP.put("1","\t•\t核查近两个月的销售发票开具时间与实际出货时间是否一致；\n" +
                "\t•\t要求工厂提供装箱单、物流单据或ERP出库记录，验证是否真实发货；\n" +
                "\t•\t重点抽查销售大幅增长的客户交易背景，排除关联交易或异常开票行为；\n" +
                "\t•\t如存在出口发票，应查验报关与结汇单据是否与销售同步。");
        SUGGESTION_MAP.put("2","\t•\t核查近两个月的销售发票开具时间与实际出货时间是否一致；\n" +
                "\t•\t要求工厂提供装箱单、物流单据或ERP出库记录，验证是否真实发货；\n" +
                "\t•\t重点抽查销售大幅增长的客户交易背景，排除关联交易或异常开票行为；\n" +
                "\t•\t如存在出口发票，应查验报关与结汇单据是否与销售同步。");
        SUGGESTION_MAP.put("3","\t•\t确认销售结构是否变化（如高单价产品短期放量），核实单价及产品组合；\n" +
                "\t•\t检查电费发票是否存在漏报、集中结算或跨期入账情形；\n" +
                "\t•\t结合产能记录，判断是否存在产而未售或售而未产的异常；\n" +
                "\t•\t如产品单耗明显偏离，应要求更新BOM并结合行业数据重核合理性。");
    }

    @Override
    public boolean process(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        IPersistenceDAO daoDWD = DAOManager.getPersistenceDAO("dataSource_DB_DWD");
        SqlHelper sql = new SqlHelper();
        sql.append("select * from DWD_FACTORY_INSPECTION_ENERGY_ONSUMPTION where usc_code = ? ",parameters.get("code"));
        List<Map> maps = daoDWD.queryForList(sql.getSql(), sql.getParamList());
        if (maps.isEmpty()){
            return false;
        }
        XWPFDocument document = (XWPFDocument)parameters.get("document");
//        String placeholder = (String)parameters.get("placeholder");
        String placeholder = "${energyConsumptionAndShipmentsCensorSuggestion}";
        List<String> typeList = new ArrayList<>();
        Map<String, String> stringStringMap = keyValue("dwd_factory_inspection_energy_onsumption");
        for (int i = 0; i < maps.size(); i++) {
            XWPFTableRow row = WordProcessorUtil.createRow(table);
            row.getCell(0).setText(StringHandleUtils.convertToChineseMonthFormat(String.valueOf(maps.get(i).get("statYearMonth"))));
            row.getCell(1).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("salesAmount"))));
            row.getCell(2).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("electricityAmount"))));
            row.getCell(3).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("sgr"))));
            row.getCell(4).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("egr"))));
            row.getCell(5).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("seRaion"))));
            row.getCell(6).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("seratioBegin")))+"-"+StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("seratioEnd"))));
            String promptContent = (String)maps.get(i).get("promptContent");
            if (StringUtils.isBlank(promptContent)){
                row.getCell(7).setText("");
            }else {
                String value = "";
                String[] split = promptContent.split(",");
                for (int j = 0; j < split.length; j++) {
                    if (!typeList.contains(split[j])){
                        typeList.add(split[j]);
                    }
                    value+= stringStringMap.get(split[j]);
                    value+= "；";
                }
                row.getCell(7).setText(value);
            }
        }

        if (typeList.isEmpty()){
            DocumentOperator.deleteParagraphByText(document,placeholder);
        }else {
            List<String> list = new ArrayList<>();
            list.add(SUGGESTION_STRING);
            for (int i = 0; i < typeList.size(); i++) {
                if ("4".equals(typeList.get(i))){
                    continue;
                }
                list.addAll(Arrays.asList(SUGGESTION_MAP.get(typeList.get(i)).split("\n")));
            }
            DocumentOperator.replacePlaceholders(document,placeholder,list);
        }
        return true;
    }


    private Map<String,String> keyValue(String dataTable) throws OptimusException {
        IPersistenceDAO daoDWD = DAOManager.getPersistenceDAO("dataSource_DB_ADS");
        SqlHelper sql = new SqlHelper();
        sql.append("select * from ADS_PROMPT_CONTENT where data_table = ? ",dataTable);
        Map<String, String> collect = daoDWD.queryForList(sql.getSql(), sql.getParamList()).stream().collect(Collectors.toMap(
                // 从每个Map中提取"id"字段作为键
                map -> (String) map.get("encode"),
                // 值就是Map本身
                map -> (String) map.get("enname"),
                // 处理键冲突：保留新值
                (existing, replacement) -> replacement
        ));
        return collect;
    }

}