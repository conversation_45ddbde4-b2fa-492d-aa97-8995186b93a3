package com.gwssi.focus.reporting.enums;

/**
 * <AUTHOR>
 * @Date 2025/6/5 19:40
 * @Description: BusinessStatus
 */
public enum BusinessStatus {
    OPEN("0", "开业"),
    BUSINESS("1", "在业"),
    SURVIVING("2", "存续"),
    REVOKE("3", "吊销"),
    LOGOUT("4", "注销"),
    MOVE_OUT("5", "迁出"),
    MOVE_IN("6", "迁入"),
    STOP("7", "停业"),
    CLEAR("8", "清算");

    private final String code;
    private final String description;

    BusinessStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // 通过代码获取枚举实例
    public static BusinessStatus fromCode(String code) {
        for (BusinessStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("无效的营业状态代码: " + code);
    }

    // 通过代码获取描述
    public String getDescription(String code) {
        BusinessStatus status = fromCode(code);
        return status.description;
    }

    // Getters
    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
