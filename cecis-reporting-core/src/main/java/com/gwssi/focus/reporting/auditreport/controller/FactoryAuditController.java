package com.gwssi.focus.reporting.auditreport.controller;



import com.gwssi.focus.reporting.auditreport.service.FactoryAuditService;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.web.event.OptimusRequest;
import com.gwssi.optimus.core.web.event.OptimusResponse;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@RestController
@RequestMapping("/api/reporting/v1/audit")
public class FactoryAuditController {
    private Logger logger = LoggerFactory.getLogger(FactoryAuditController.class.getName());

    @Autowired
    private FactoryAuditService factoryAuditService;


    @RequestMapping("/generateAuditReport")
    public void getCodeList(OptimusRequest request, OptimusResponse response) throws OptimusException, IOException, InvalidFormatException {
        String companyName = (String)request.getAttr("companyName");
        String auditReportDate = (String)request.getAttr("auditReportDate");
        factoryAuditService.generateReport(companyName,auditReportDate);
        response.addAttr("result", "success");
    }

}


