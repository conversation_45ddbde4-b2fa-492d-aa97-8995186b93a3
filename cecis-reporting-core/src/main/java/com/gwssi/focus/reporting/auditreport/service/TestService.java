package com.gwssi.focus.reporting.auditreport.service;


import com.gwssi.focus.reporting.WordProcessor;
import com.gwssi.focus.reporting.auditreport.WordProcessorUtilFactory;
import com.gwssi.focus.reporting.auditreport.impl.*;
import com.gwssi.focus.reporting.utils.WordProcessorUtil;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.service.BaseService;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class TestService extends BaseService {
    @Autowired
    private EnergyConsumptionAndShipmentsCensorExcel energyConsumptionAndShipmentsCensorExcel;
    @Autowired
    private EnergyConsumptionInvoiceExcel energyConsumptionInvoiceExcel;
    @Autowired
    private FactoryEnergyConsumptionExcel factoryEnergyConsumptionExcel;
    @Autowired
    private FreezeEntryExcel freezeEntryExcel;
    @Autowired
    private InputInvoiceExcel inputInvoiceExcel;
    @Autowired
    private InputProductsAnalyseExcel inputProductsAnalyseExcel;
    @Autowired
    private OutputInvoiceExcel outputInvoiceExcel;
    @Autowired
    private ScreeningResultsExcel screeningResultsExcel;
    @Autowired
    private Top10InputMaterialExcel top10InputMaterialExcel;
    @Autowired
    private Top10OutputProductExcel top10OutputProductExcel;
    @Autowired
    private Top10ProviderExcel top10ProviderExcel;
    @Autowired
    private WordProcessorUtilFactory factory;





    public void generateReport(String companyName) throws OptimusException, IOException, InvalidFormatException {
        // 模板路径和输出路径
        String templatePath = "D:\\gwdata\\验厂模型汇报-0521tiger.docx";
        IPersistenceDAO daoDWD = getPersistenceDAO("DB_DWD");
        IPersistenceDAO daoODS = getPersistenceDAO("DB_ODS");
        IPersistenceDAO daoADS = DAOManager.getPersistenceDAO("DB_ADS");
        SqlHelper sql = new SqlHelper().append("select * from DWD_FACTORY_INSPECTION_DETAILS where 1=1 ");
        sql.appendIfNotBlank(" and name = ? ", companyName);
        List<Map> maps = daoDWD.queryForList(sql.getSql(), sql.getParamList());
        for (int i = 0; i < maps.size(); i++) {
            String outputPath = "D:\\gwdata\\验厂模型汇报-"+maps.get(i).get("name")+".docx";

            // 从工厂获取实例并确保初始化
            WordProcessorUtil processor = factory.getInstance(templatePath);
            processor.init(templatePath);
            Map<String, Object> tmp = new HashMap<>();
            tmp.put("company",String.valueOf(maps.get(i).get("name")));
            tmp.put("code",String.valueOf(maps.get(i).get("uscCode")));
            tmp.put("processor",processor);
            // 替换表格
            processor.importWordTable("${energyConsumptionAndShipmentsCensorExcel}", "D:\\gwdata\\template\\全部模板.docx",0,energyConsumptionAndShipmentsCensorExcel,tmp);
            processor.importWordTable("${energyConsumptionInvoiceExcel}", "D:\\gwdata\\template\\全部模板.docx",8,energyConsumptionInvoiceExcel,tmp);
            processor.importWordTable("${factoryEnergyConsumptionExcel}", "D:\\gwdata\\template\\全部模板.docx",1,factoryEnergyConsumptionExcel,tmp);
            processor.importWordTable("${freezeEntryExcel}", "D:\\gwdata\\template\\全部模板.docx",2,freezeEntryExcel,tmp);
            processor.importWordTable("${inputInvoiceExcel}", "D:\\gwdata\\template\\全部模板.docx",8,inputInvoiceExcel,tmp);
            processor.importWordTable("${inputProductsAnalyseExcel}", "D:\\gwdata\\template\\全部模板.docx",3,inputProductsAnalyseExcel,tmp);
            processor.importWordTable("${outputInvoiceExcel}", "D:\\gwdata\\template\\全部模板.docx",8,outputInvoiceExcel,tmp);
            processor.importWordTable("${screeningResultsExcel}", "D:\\gwdata\\template\\全部模板.docx",4,screeningResultsExcel,tmp);
            processor.importWordTable("${top10InputMaterialExcel}", "D:\\gwdata\\template\\全部模板.docx",5,top10InputMaterialExcel,tmp);
            processor.importWordTable("${top10OutputProductExcel}", "D:\\gwdata\\template\\全部模板.docx",6,top10OutputProductExcel,tmp);
            processor.importWordTable("${top10ProviderExcel}", "D:\\gwdata\\template\\全部模板.docx",7 ,top10ProviderExcel,tmp);


            // 判断文件是pdf还是图片taxReturnsPng：纳税申报 dutiableProvePng：完税证明
            // TODO 因为没有真实数据所以就拿这一个处理
            // 循环3次，假设有三年的数据
            for (int j = 0; j < 2; j++) {
                // 插入PDF第一页内容（转图片）
                processor.insertPdfFirstPageAsImage("${taxReturnsPng}", "D:\\gwdata\\纳税申报.pdf",200,300);
            }
            // 替换图片
            processor.replaceImage("${dutiableProvePng}", "D:\\gwdata\\完税证明.jpg", 200, 200);
            // 替换文本
            processor.replaceText("${companyName}", String.valueOf(maps.get(i).get("name")));
            processor.replaceText("${reportDate}", getData());
            processor.replaceText("${reportYear}", getYear());
            processor.replaceText("${reportRandom}", generate5DigitString());
            processor.replaceText("${companySocial}", String.valueOf(maps.get(i).get("uscCode")));
            processor.replaceText("${taskNumber}", String.valueOf(maps.get(i).get("number")));
            sql.clear();
            sql.append("select case when INDICATOR_VALUE > 0 then '1' else '0' end as INDICATOR_VALUE_STR from ADS_FACTORY_INSPECTION_RESULTS " +
                    " where name = ? and indicator_hierarchy = 1 ", String.valueOf(maps.get(i).get("name")));
            processor.replaceText("${conclusion}", "0".equals(daoADS.queryForList(sql.getSql(), sql.getParamList()).get(0).get("indicatorValueStr"))?"通过":"不通过");
            processor.replaceText("${managementState}", String.valueOf(maps.get(i).get("managementName")));
            processor.replaceText("${isConsistency}", String.valueOf(maps.get(i).get("plantAddrtype")).equals("unanimous")?"否":"是");
            processor.replaceText("${taxpayerType}", String.valueOf(maps.get(i).get("taxpayerType")));
            processor.replaceText("${isDishonesty}", String.valueOf(maps.get(i).get("isDishonesty")).equals("0")?"否":"是");
            processor.replaceText("${isRestrictionHighConsumption}", String.valueOf(maps.get(i).get("isRestrictionHighConsumption")).equals("0")?"否":"是");
            processor.replaceText("${isBlacklist}", String.valueOf(maps.get(i).get("isBlacklist")).equals("0")?"否":"是");
            processor.replaceText("${isSanctions}", String.valueOf(maps.get(i).get("isSanctions")).equals("0")?"否":"是");
            sql.clear();
            sql.append("select distinct goods_taxable_services from ods_output_invoices where seller_name = ? ",String.valueOf(maps.get(i).get("name")));
            String productList = daoODS.queryForList(sql.getSql(),sql.getParamList()).stream()
                    .map(map -> map.get("goodsTaxableServices"))
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .collect(Collectors.joining("、"));
            processor.replaceText("${productList}", productList);
            sql.clear();
            sql.append("select sum(total_amount_including_tax) from ods_input_invoice where purchaser_name = ? and invoice_date >= ADD_MONTHS(SYSDATE, -6) and goods_taxable_services not like '%电费%' and goods_taxable_services not like '%电服务费%' and goods_taxable_services not like '%水费%' and goods_taxable_services not like '%水服务费%' ",String.valueOf(maps.get(i).get("name")));
            long inputAmountTax = daoODS.queryForLong(sql.getSql(), sql.getParamList());
            processor.replaceText("${inputAmountTax}", String.valueOf(inputAmountTax));

            sql.clear();
            sql.append("select SUPPLIER_NAME from DWD_FACTORY_SUPPLIER_DETAILED where name = ? order by SUPPLIER_PROCUREMENT_AMOUNT_SORTING limit 10 ",String.valueOf(maps.get(i).get("name")));
            String supplierName = daoDWD.queryForList(sql.getSql(), sql.getParamList()).stream()
                    .map(map -> map.get("supplierName"))
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .collect(Collectors.joining("','"));
            sql.clear();
            sql.append("select max(TO_CHAR(invoice_date, 'YYYY-MM-DD')) as invoice_date from ods_input_invoice where seller_name in ('"+supplierName+"') and goods_taxable_services not like '%电费%' and goods_taxable_services not like '%电服务费%' and goods_taxable_services not like '%水费%' and goods_taxable_services not like '%水服务费%' ");
            processor.replaceText("${inputEndTime}", String.valueOf(daoODS.queryForList(sql.getSql(), sql.getParamList()).get(0).get("invoiceDate")));
            sql.clear();
            sql.append("select verification_result from ods_input_invoice where purchaser_name = ? and verification_result = 1 and goods_taxable_services not like '%电费%' and goods_taxable_services not like '%电服务费%' and goods_taxable_services not like '%水费%' and goods_taxable_services not like '%水服务费%'",String.valueOf(maps.get(i).get("name")));
            processor.replaceText("${counterfeitingInput}", daoODS.queryForList(sql.getSql(), sql.getParamList()).isEmpty()?"通过":"不通过");
            sql.clear();
            sql.append("select verification_result from ods_output_invoices where seller_name = ? and verification_result = 1 ",String.valueOf(maps.get(i).get("name")));
            processor.replaceText("${counterfeitingOutput}", daoODS.queryForList(sql.getSql(), sql.getParamList()).isEmpty()?"通过":"不通过");
            sql.clear();
            sql.append("select verification_result from ods_input_invoice where purchaser_name = ? and verification_result = 1 and (goods_taxable_services like '%电费%' or goods_taxable_services like '%电服务费%' or goods_taxable_services like '%水费%' or goods_taxable_services like '%水服务费%')",String.valueOf(maps.get(i).get("name")));
            processor.replaceText("${counterfeitingEnergyConsumption}", daoODS.queryForList(sql.getSql(), sql.getParamList()).isEmpty()?"通过":"不通过");
            // 保存文档

            processor.save(outputPath);
            System.out.println("完成");
        }

    }

    public static String getData() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 定义格式化模式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年M月d日", Locale.CHINA);
        // 格式化日期
        String formattedDate = currentDate.format(formatter);
        return formattedDate;
    }
    public static String getYear() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 提取年份（返回int类型）
        int year = currentDate.getYear();
        // 转换为字符串（如需字符串格式）
        String yearStr = String.valueOf(year);
        return yearStr;
    }

    public static String generate5DigitString() {
        Random random = new Random();
        // 生成0-99999之间的随机数
        int randomNum = random.nextInt(100000);
        // 格式化为5位字符串，不足前补零
        return String.format("%05d", randomNum);
    }

}
