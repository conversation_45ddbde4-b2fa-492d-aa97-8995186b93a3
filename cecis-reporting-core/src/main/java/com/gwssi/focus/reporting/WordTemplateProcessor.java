package com.gwssi.focus.reporting;

import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import java.io.*;
import java.util.List;
import java.util.Map;

public class WordTemplateProcessor {

    // 1. 图片替换（支持多图动态插入）
    public static void replaceImages(XWPFDocument doc, Map<String, String> imageMap) throws Exception {
        for (Map.Entry<String, String> entry : imageMap.entrySet()) {
            String placeholder = entry.getKey();
            String imagePath = entry.getValue();

            for (XWPFParagraph p : doc.getParagraphs()) {
                for (XWPFRun r : p.getRuns()) {
                    String text = r.getText(0);
                    if (text != null && text.contains(placeholder)) {
                        replaceImageInRun(r, placeholder, imagePath);
                    }
                }
            }
        }
    }

    private static void replaceImageInRun(XWPFRun run, String placeholder, String imagePath) throws Exception {
        int width = 400; // 图片宽度（单位：像素）
        int height = 300; // 图片高度

        InputStream is = new FileInputStream(imagePath);
        run.addPicture(is,
                XWPFDocument.PICTURE_TYPE_PNG,
                imagePath,
                Units.toEMU(width),
                Units.toEMU(height));
        is.close();
        run.setText(run.getText(0).replace(placeholder, ""));
    }

    // 2. 表格动态生成（样式复用）
    public static void createTables(XWPFDocument doc, List<String[]> tableDataList) {
        for (String[] data : tableDataList) {
            XWPFTable table = doc.createTable();
            applyTableStyle(table);

            XWPFTableRow headerRow = table.getRow(0);
            for (String header : data) {
                headerRow.addNewTableCell().setText(header);
            }
        }
    }

    private static void applyTableStyle(XWPFTable table) {
        table.setWidth("100%");
        table.getCTTbl().getTblPr().unsetTblBorders();
        CTTblBorders borders = table.getCTTbl().addNewTblPr().addNewTblBorders();
        borders.addNewTop().setVal(STBorder.SINGLE);
        // 其他样式设置...
    }

    // 3. 文本替换
    public static void replaceText(XWPFDocument doc, Map<String, String> replacements) {
        replacements.forEach((search, replace) -> {
            doc.getParagraphs().forEach(p -> {
                List<XWPFRun> runs = p.getRuns();
                for (int i = 0; i < runs.size(); i++) {
                    XWPFRun run = runs.get(i);
                    String text = run.getText(0);
                    if (text != null && text.contains(search)) {
                        text = text.replace(search, replace);
                        run.setText(text, 0);
                    }
                }
            });
        });
    }

    // 4. PDF内容提取插入
    public static void insertPdfContent(XWPFDocument doc, String pdfPath) throws Exception {
        PDDocument pdfDoc = PDDocument.load(new File(pdfPath));
        PDFTextStripper stripper = new PDFTextStripper();
        String text = stripper.getText(pdfDoc);
        pdfDoc.close();

        XWPFParagraph p = doc.createParagraph();
        XWPFRun run = p.createRun();
        run.setText(text);
    }

    // 5. Excel表格导入（示例使用CSV格式）
    public static void insertExcelTable(XWPFDocument doc, String csvPath) throws IOException {
        List<String[]> data = CsvUtils.readCsv(csvPath);
        XWPFTable table = doc.createTable();
        for (String[] row : data) {
            XWPFTableRow tableRow = table.createRow();
            for (String cell : row) {
                tableRow.getCell(0).setText(cell);
                tableRow.addNewTableCell();
            }
        }
    }

//    // 6. 艺术字处理（使用文本框）
//    public static void insertWordArt(XWPFDocument doc, String text) throws Exception {
//        // 创建文本框
//        CTTextbox textbox = CTTextbox.Factory.newInstance();
//        CTTextParagraph p = textbox.addNewP();
//        CTR run = p.addNewR();
//        run.addNewT().setStringValue(text);
//
//        // 添加样式
//        run.addNewRPr().addNewB(); // 加粗
//        run.addNewRPr().addNewColor().setVal("FF0000"); // 红色文字
//
//        // 插入文档
//        XWPFParagraph containerPara = doc.createParagraph();
//        XWPFRun containerRun = containerPara.createRun();
//        containerRun.getCTR().addNewDrawing().set(textbox);
//    }
//    public static void insertWordArtVML(XWPFDocument doc, String text) throws Exception {
//        // 创建VML图形对象
//        CTGroup group = doc.getDocument().getBody().addNewVml().addNewGroup();
//        CTShape shape = group.addNewShape();
//        shape.setId("_x0000_s1025");
//        shape.setStyle("position:absolute;left:0;top:0;width:200pt;height:50pt");
//
//        // 添加文本路径
//        CTTextPath textPath = shape.addNewTextPath();
//        textPath.setString(text);
//        textPath.setStyle("font-family:Arial;font-weight:bold;font-size:24pt");
//
//        // 设置填充颜色
//        CTFill fill = shape.addNewFill();
//        fill.setColor2("#FF0000"); // 红色填充
//    }
}