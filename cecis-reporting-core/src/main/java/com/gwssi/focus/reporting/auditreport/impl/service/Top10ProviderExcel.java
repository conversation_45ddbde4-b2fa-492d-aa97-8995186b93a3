package com.gwssi.focus.reporting.auditreport.impl.service;


import com.gwssi.focus.reporting.auditreport.impl.BusinessProcessing;
import com.gwssi.focus.reporting.enums.BusinessStatus;
import com.gwssi.focus.reporting.utils.StringHandleUtils;
import com.gwssi.focus.reporting.utils.WordProcessorUtil;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/21 16:31
 * @Description: 工厂的采购金额（含税金额）占比排名前10的供应商清单信息
 */
@Component
public class Top10ProviderExcel implements BusinessProcessing {

    @Override
    public boolean process(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        // TODO 却时间判断
        IPersistenceDAO daoDWD = DAOManager.getPersistenceDAO("dataSource_DB_DWD");
        SqlHelper sql = new SqlHelper();

        sql.append("select *,case when supplier_equity_amount >= 20 then '1' else '0' end as is_supplier_equity_amount " +
                "from dwd_factory_supplier_detailed where usc_code = ? and number = ? order by SUPPLIER_PROCUREMENT_AMOUNT_SORTING limit 10 ",parameters.get("code"),parameters.get("number"));
        List<Map> maps = daoDWD.queryForList(sql.getSql(), sql.getParamList());
        if (maps.isEmpty()){
            return false;
        }



        for (int i = 0; i < maps.size(); i++) {
            XWPFTableRow row = WordProcessorUtil.createRow(table);
//            XWPFTableRow row = table.createRow();
            row.getCell(0).setText(String.valueOf(i+1));
            row.getCell(1).setText(String.valueOf(maps.get(i).get("supplierName")));
            row.getCell(2).setText((String)maps.get(i).get("supplierUscCode"));
            row.getCell(3).setText(BusinessStatus.fromCode((String)maps.get(i).get("supplierManagementState")).getDescription());
            row.getCell(4).setText((String)maps.get(i).get("productsStr"));
            row.getCell(5).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("supplierProcurementAmount"))));
            row.getCell(6).setText("0".equals(String.valueOf(maps.get(i).get("isSupplierBlacklist")))?"否":"是");
            row.getCell(7).setText("0".equals(String.valueOf(maps.get(i).get("isSupplierDishonesty")))?"否":"是");
            row.getCell(8).setText("0".equals(String.valueOf(maps.get(i).get("isSupplierRestrictionHighConsumption")))?"否":"是");
            row.getCell(9).setText("0".equals(String.valueOf(maps.get(i).get("isSupplierEquityAmount")))?"否":"是");
        }

//        for (XWPFTableRow row : table.getRows()) {
//            for (XWPFTableCell cell : row.getTableCells()) {
//                for (XWPFParagraph paragraph : cell.getParagraphs()) {
//                    for (XWPFRun run : paragraph.getRuns()) {
//                        // 设置字体大小（注意：POI中字号需要转换为TWIPS单位）
//                        run.setFontSize(9); // 1磅 = 20 TWIPS
//
////                         同时设置中文字体（可选）
////                        run.setFontFamily("宋体");
//                    }
//                }
//            }
//        }
        return true;
    }



}