package com.gwssi.focus.reporting.auditreport.controller;



import com.gwssi.focus.reporting.auditreport.service.TestService;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.web.event.OptimusRequest;
import com.gwssi.optimus.core.web.event.OptimusResponse;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/reporting/v1/test")
public class TestController {
    private Logger logger = LoggerFactory.getLogger(TestController.class.getName());

    @Autowired
    private TestService testService;


    @RequestMapping("/getCodeList")
    public void getCodeList(OptimusRequest request, OptimusResponse response) throws OptimusException, IOException, InvalidFormatException {
        String companyName = (String)request.getAttr("companyName");
        testService.generateReport(companyName);
        response.addAttr("result", "success");
    }

}


