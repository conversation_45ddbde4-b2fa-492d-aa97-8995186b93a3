package com.gwssi.focus.reporting.auditreport.impl.service;

import com.gwssi.focus.reporting.auditreport.impl.BusinessProcessing;
import com.gwssi.focus.reporting.utils.BigDecimalUtils;
import com.gwssi.focus.reporting.utils.StringHandleUtils;
import com.gwssi.focus.reporting.utils.WordProcessorUtil;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/21 16:31
 * @Description: 销项-验伪不通过的发票明细
 */
@Component
public class OutputInvoiceExcel implements BusinessProcessing {

    @Override
    public boolean process(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        IPersistenceDAO daoODS = DAOManager.getPersistenceDAO("dataSource_DB_ODS");
        SqlHelper sql = new SqlHelper();
        sql.append("select t.*,TO_CHAR(invoice_date, 'YYYY-MM-DD') as invoice_date_str from ods_output_invoices t where seller_name = ? and verification_result = 1",parameters.get("company"));
        List<Map> maps = daoODS.queryForList(sql.getSql(), sql.getParamList());
        if (maps.isEmpty()){
            return false;
        }
        BigDecimal total = BigDecimal.ZERO;
        for (int i = 0; i < maps.size(); i++) {
            XWPFTableRow row = WordProcessorUtil.createRow(table);
            row.getCell(0).setText(String.valueOf(i+1));
            row.getCell(1).setText(String.valueOf(maps.get(i).get("invoiceCode")));
            row.getCell(2).setText(String.valueOf(maps.get(i).get("invoiceNumber")));
            row.getCell(3).setText(String.valueOf(maps.get(i).get("digitalTicketNumber")));
            row.getCell(4).setText(String.valueOf(maps.get(i).get("sellerIdentificationNumber")));
            row.getCell(5).setText(String.valueOf(maps.get(i).get("sellerName")));
            row.getCell(6).setText(String.valueOf(maps.get(i).get("purchaserIdentificationNumber")));
            row.getCell(7).setText(String.valueOf(maps.get(i).get("purchaserName")));
            row.getCell(8).setText(String.valueOf(maps.get(i).get("invoiceDateStr")));
            row.getCell(9).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("amount"))));
            row.getCell(10).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("taxAmount"))));
            row.getCell(11).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("totalAmountIncludingTax"))));
            row.getCell(12).setText(String.valueOf(maps.get(i).get("invoiceSource")));
            row.getCell(13).setText(String.valueOf(maps.get(i).get("invoiceType")));
            row.getCell(14).setText(String.valueOf(maps.get(i).get("invoiceStatus")));
            row.getCell(15).setText(String.valueOf(maps.get(i).get("invoiceRiskLevel")));
            row.getCell(16).setText(String.valueOf(maps.get(i).get("drawer")));
            row.getCell(17).setText(String.valueOf(maps.get(i).get("remarks")));
            if (null != maps.get(i).get("totalAmountIncludingTax")){
                total = BigDecimalUtils.safeAdd(total,new BigDecimal(String.valueOf(maps.get(i).get("totalAmountIncludingTax"))));
            }
        }
        WordProcessorUtil processor = (WordProcessorUtil)parameters.get("processor");
        processor.replaceText("${outPutAmountSum}",total.toString());
        return true;
    }

}