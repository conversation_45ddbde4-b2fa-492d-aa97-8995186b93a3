package com.gwssi.focus.reporting.utils;

/**
 * <AUTHOR>
 * @Date 2025/6/5 9:52
 * @Description: StringUtils
 */
public class StringHandleUtils {

    public static String convertToChineseMonthFormat(String input) {
        // 校验输入格式：必须为6位数字
        if (!input.matches("\\d{6}")) {
            return input;
//            throw new IllegalArgumentException("输入格式错误！必须为6位数字（如202501）");
        }

        try {
            // 解析年份和月份
            int year = Integer.parseInt(input.substring(0, 4));
            int month = Integer.parseInt(input.substring(4, 6));

            // 校验月份有效性（1-12月）
            if (month < 1 || month > 12) {
                return input;
//                throw new IllegalArgumentException("月份无效！必须为1-12之间的数字");
            }

            // 格式化为目标字符串
            return String.format("%d年%02d月", year, month);
        } catch (NumberFormatException e) {
            return input;
//            throw new IllegalArgumentException("输入包含非数字字符！", e);
        }
    }

    public static String formatNumber(String input) {
        // 处理空值或空字符串
        if (input == null || input.isEmpty()) {
            return input;
        }

        // 直接返回"0"
        if ("0".equals(input)) {
            return "0";
        }

        // 处理包含小数点的情况
        if (input.contains(".")) {
            // 分割整数部分和小数部分
            String[] parts = input.split("\\.", 2);
            String decimalPart = parts[1];

            // 去除小数部分末尾的零
            decimalPart = decimalPart.replaceAll("0+$", "");

            // 如果小数部分为空，返回整数部分
            if (decimalPart.isEmpty()) {
                return parts[0];
            }

            // 否则返回整数部分和处理后的小数部分
            return parts[0] + "." + decimalPart;
        }

        // 不包含小数点，直接返回原字符串（可能是整数或非数字）
        return input;
    }
}
