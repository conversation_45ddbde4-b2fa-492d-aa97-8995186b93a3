package com.gwssi.focus.reporting.common;

import org.apache.poi.xwpf.usermodel.XWPFDocument;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @Date 2025/6/6 11:14
 * @Description: DocumentBuilder 用于从文件路径构建 XWPFDocument 的工具类
 */
public class DocumentBuilder {

    /**
     * 根据文件路径加载并构建 XWPFDocument 对象
     * @param filePath Word 文件路径（.docx）
     * @return XWPFDocument 对象
     * @throws IOException 文件读取异常
     * @throws IllegalArgumentException 文件格式不支持或路径无效
     */
    public static XWPFDocument build(String filePath) throws IOException {
        if (filePath == null || filePath.trim().isEmpty()) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        try (InputStream is = new FileInputStream(filePath)) {
            return new XWPFDocument(is);
        } catch (Exception e) {
            throw new IOException("加载 Word 文档失败，路径：" + filePath, e);
        }
    }
}
