package com.gwssi.focus.reporting.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hslf.usermodel.*;
import org.apache.poi.xslf.usermodel.*;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Dimension2D;
import java.awt.image.BufferedImage;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;


public class PptProcessorUtil {

    public static void main(String[] args) {
        try {
            // 示例：处理PPT文件
            processFile("D:\\gwdata\\ppt模板.pptx", "D:\\gwdata\\output.jpg", "17", "新文本*", Color.RED);
//            processPptFile("D:\\gwdata\\ppt模板.pptx", "D:\\gwdata\\output.jpg", "17", "新文本", Color.RED);
            System.out.println("PPT处理完成！");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理PPT/PPTX文件：根据扩展名选择正确的API
     */
    public static void processFile(String inputFilePath, String outputFilePath,
                            String oldText, String newText, Color textColor) throws IOException {
        String fileExtension = getFileExtension(inputFilePath).toLowerCase();

        switch (fileExtension) {
            case "ppt":
                processPptFile(inputFilePath, outputFilePath, oldText, newText, textColor);
                break;
            case "pptx":
                processPptxFile(inputFilePath, outputFilePath, oldText, newText, textColor);
                break;
            default:
                throw new IllegalArgumentException("不支持的文件格式: " + fileExtension);
        }
    }

    /**
     * 处理PPT/PPTX文件：根据扩展名选择正确的API
     */
    public static void processFile(String inputFilePath, String outputFilePath,
                                   Map<String,Map> changeMap, Color textColor) throws IOException {
        String fileExtension = getFileExtension(inputFilePath).toLowerCase();

        switch (fileExtension) {
            case "ppt":
                processPptFile(inputFilePath, outputFilePath, changeMap, textColor);
                break;
            case "pptx":
                processPptxFile(inputFilePath, outputFilePath, changeMap, textColor);
                break;
            default:
                throw new IllegalArgumentException("不支持的文件格式: " + fileExtension);
        }
    }

    /**
     * 处理旧版PPT文件 (.ppt)
     */
    private static void processPptFile(String inputFilePath, String outputFilePath,
                                       Map<String,Map> changeMap, Color textColor) throws IOException {
        try (FileInputStream fis = new FileInputStream(inputFilePath);
             HSLFSlideShow ppt = new HSLFSlideShow(fis)) {

            List<HSLFSlide> slides = ppt.getSlides();
            if (!slides.isEmpty()) {
                HSLFSlide slide = slides.get(0);
                replaceTextAndChangeColor(slide, changeMap, textColor);
                BufferedImage image = convertSlideToImage(slide, 1200, 800);
                saveImage(image, outputFilePath);
            }
        }
    }

    /**
     * 处理旧版PPT文件 (.ppt)
     */
    private static void processPptFile(String inputFilePath, String outputFilePath,
                                       String oldText, String newText, Color textColor) throws IOException {
        try (FileInputStream fis = new FileInputStream(inputFilePath);
             HSLFSlideShow ppt = new HSLFSlideShow(fis)) {

            List<HSLFSlide> slides = ppt.getSlides();
            if (!slides.isEmpty()) {
                HSLFSlide slide = slides.get(0);
                replaceTextAndChangeColor(slide, oldText, newText, textColor);
                BufferedImage image = convertSlideToImage(slide, 1200, 800);
                saveImage(image, outputFilePath);
            }
        }
    }

    /**
     * 处理新版PPTX文件 (.pptx)
     */
    private static void processPptxFile(String inputFilePath, String outputFilePath,
                                        Map<String,Map> changeMap, Color textColor) throws IOException {
        try (FileInputStream fis = new FileInputStream(inputFilePath);
             XMLSlideShow ppt = new XMLSlideShow(fis)) {

            List<XSLFSlide> slides = ppt.getSlides();
            if (!slides.isEmpty()) {
                XSLFSlide slide = slides.get(0);
                replaceTextAndChangeColor2(slide, changeMap, textColor);
                BufferedImage image = convertSlideToImage2(slide, 1200, 800);
                saveImage(image, outputFilePath);
            }
        }
    }

    /**
     * 处理新版PPTX文件 (.pptx)
     */
    private static void processPptxFile(String inputFilePath, String outputFilePath,
                                        String oldText, String newText, Color textColor) throws IOException {
        try (FileInputStream fis = new FileInputStream(inputFilePath);
             XMLSlideShow ppt = new XMLSlideShow(fis)) {

            List<XSLFSlide> slides = ppt.getSlides();
            if (!slides.isEmpty()) {
                XSLFSlide slide = slides.get(0);
                replaceTextAndChangeColor2(slide, oldText, newText, textColor);
                BufferedImage image = convertSlideToImage2(slide, 1200, 800);
                saveImage(image, outputFilePath);
            }
        }
    }

    /**
     * 获取文件扩展名
     */
    private static String getFileExtension(String fileName) {
        int lastIndexOf = fileName.lastIndexOf(".");
        if (lastIndexOf == -1) {
            return ""; // 无扩展名
        }
        return fileName.substring(lastIndexOf + 1);
    }
//
//    /**
//     * 处理PPT文件：替换文字、修改颜色并转换为图片
//     *
//     * @param inputFilePath  输入PPT文件路径
//     * @param outputFilePath 输出图片文件路径
//     * @param oldText        需要替换的旧文本
//     * @param newText        替换后的新文本
//     * @param textColor      文本颜色
//     * @throws IOException 如果文件操作失败
//     */
//    public static void processPptFile(String inputFilePath, String outputFilePath,
//                                      String oldText, String newText, Color textColor) throws IOException {
//        // 读取PPT文件
//        try (FileInputStream fis = new FileInputStream(inputFilePath);
//             HSLFSlideShow ppt = new HSLFSlideShow(fis)) {
//
//            // 获取所有幻灯片
//            List<HSLFSlide> slides = ppt.getSlides();
//
//            // 处理第一张幻灯片（索引0）
//            if (!slides.isEmpty()) {
//                HSLFSlide slide = slides.get(0);
//
//                // 替换文字并修改颜色
//                replaceTextAndChangeColor(slide, oldText, newText, textColor);
//
//                // 将幻灯片转换为图片
//                BufferedImage image = convertSlideToImage(slide, 300, 200);
//
//                // 保存图片
//                saveImage(image, outputFilePath);
//            }
//        }
//    }

    /**
     * 替换幻灯片中的文字并修改颜色
     *
     * @param slide     幻灯片对象
     * @param oldText   需要替换的旧文本
     * @param newText   替换后的新文本
     * @param textColor 文本颜色
     */
    /**
     * 替换幻灯片中的文字并修改颜色 (通用方法，适用于HSLF和XSLF)
     */
//    private <S extends Slide<S, P>, P extends TextParagraph<S, P, R>, R extends TextRun>
//    void replaceTextAndChangeColor(S slide, String oldText, String newText, Color textColor) {
//        for (Shape shape : slide.getShapes()) {
//            if (shape instanceof TextShape) {
//                TextShape textShape = (TextShape) shape;
//                for (P paragraph : textShape.getTextParagraphs()) {
//                    for (R textRun : paragraph.getTextRuns()) {
//                        String text = textRun.getText();
//                        if (text != null && text.contains(oldText)) {
//                            textRun.setText(text.replace(oldText, newText));
//                            textRun.setFontColor(textColor);
//                        }
//                    }
//                }
//            }
//        }
//    }
    private static void replaceTextAndChangeColor2(XSLFSlide slide,Map<String,Map> changeMap, Color textColor) {
        // 遍历幻灯片中的所有形状
        int y = 0;
        for (XSLFShape shape : slide.getShapes()) {
            // 只处理文本形状
            if (shape instanceof XSLFTextShape) {
                XSLFTextShape textShape = (XSLFTextShape) shape;

                // 遍历文本框中的所有段落
                for (XSLFTextParagraph paragraph : textShape) {
                    // 遍历段落中的所有文本运行
                    for (XSLFTextRun textRun : paragraph.getTextRuns()) {
                        String text = textRun.getRawText();
                        for (String oldText : changeMap.keySet()) {
                            String newText = (String) changeMap.get(oldText).get("metricRules");
                            if (text != null && text.contains(oldText) && StringUtils.equalsAny(text,"A0005","A0012","A0008","A0017")) {

                                // 设置新文本
                                textRun.setText(text.replace(text, newText));
                                break;
                            }
                            // 替换文本
                            if (text != null && text.contains(oldText)) {
                                if ("0".equals(changeMap.get(oldText).get("indicatorValueStr"))){
                                    // 设置新文本
                                    textRun.setText(text.replace(oldText, newText));

                                }else {
                                    textRun.setText(text.replace(oldText, "*" + newText));
                                    // 修改文本颜色
                                    textRun.setFontColor(textColor);
                                    y++;
                                }
                                break;
                            }
                        }
                    }
                }
            }
        }
        for (XSLFShape shape : slide.getShapes()) {
            // 只处理文本形状
            if (shape instanceof XSLFTextShape) {
                XSLFTextShape textShape = (XSLFTextShape) shape;

                // 遍历文本框中的所有段落
                for (XSLFTextParagraph paragraph : textShape) {
                    // 遍历段落中的所有文本运行
                    for (XSLFTextRun textRun : paragraph.getTextRuns()) {
                        String text = textRun.getRawText();
                        if (text != null && text.contains("X")) {
                            textRun.setText(text.replace("X", String.valueOf(20-y)));
                        }
                        if (text != null && text.contains("Y")) {
                            textRun.setText(text.replace("Y", String.valueOf(y)));
                        }
                    }
                }
            }
        }

    }


    private static void replaceTextAndChangeColor2(XSLFSlide slide, String oldText, String newText, Color textColor) {
        // 遍历幻灯片中的所有形状
        for (XSLFShape shape : slide.getShapes()) {
            // 只处理文本形状
            if (shape instanceof XSLFTextShape) {
                XSLFTextShape textShape = (XSLFTextShape) shape;

                // 遍历文本框中的所有段落
                for (XSLFTextParagraph paragraph : textShape) {
                    // 遍历段落中的所有文本运行
                    for (XSLFTextRun textRun : paragraph.getTextRuns()) {
                        String text = textRun.getRawText();

                        // 替换文本
                        if (text != null && text.contains(oldText)) {
                            // 设置新文本
                            textRun.setText(text.replace(oldText, newText));

                            // 修改文本颜色
                            textRun.setFontColor(textColor);
                        }
                    }
                }
            }
        }
    }

    private static void replaceTextAndChangeColor(HSLFSlide slide, Map<String,Map> changeMap, Color textColor) {
        // 遍历幻灯片中的所有形状
        for (String oldText : changeMap.keySet()) {
            String newText = (String) changeMap.get(oldText).get("metricRules");
            // 遍历幻灯片中的所有形状
            for (HSLFShape shape : slide.getShapes()) {
                // 只处理文本形状
                if (shape instanceof HSLFTextShape) {
                    HSLFTextShape textShape = (HSLFTextShape) shape;

                    // 遍历文本框中的所有段落
                    for (HSLFTextParagraph paragraph : textShape.getTextParagraphs()) {
                        // 遍历段落中的所有文本运行
                        for (HSLFTextRun textRun : paragraph.getTextRuns()) {
                            // POI 4.1.2 中获取文本的正确方法
                            String text = textRun.getRawText(); // 4.1.2版本使用getRawText()

                            // 替换文本
                            if (text.contains(oldText)) {
                                if ("0".equals(changeMap.get(oldText).get("indicatorValueStr"))){
                                    // 设置新文本
                                    textRun.setText(text.replace(oldText, newText));
                                }else {
                                    textRun.setText(text.replace(oldText, "* " + newText));
                                    // 修改文本颜色
                                    textRun.setFontColor(textColor);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private static void replaceTextAndChangeColor(HSLFSlide slide, String oldText, String newText, Color textColor) {
        // 遍历幻灯片中的所有形状
        for (HSLFShape shape : slide.getShapes()) {
            // 只处理文本形状
            if (shape instanceof HSLFTextShape) {
                HSLFTextShape textShape = (HSLFTextShape) shape;

                // 遍历文本框中的所有段落
                for (HSLFTextParagraph paragraph : textShape.getTextParagraphs()) {
                    // 遍历段落中的所有文本运行
                    for (HSLFTextRun textRun : paragraph.getTextRuns()) {
                        // POI 4.1.2 中获取文本的正确方法
                        String text = textRun.getRawText(); // 4.1.2版本使用getRawText()

                        // 替换文本
                        if (text.contains(oldText)) {
                            // POI 4.1.2 中设置文本的方法
                            textRun.setText(text.replace(oldText, newText));

                            // 修改文本颜色 - POI 4.1.2 版本
                            textRun.setFontColor(textColor); // 直接设置颜色
                        }
                    }
                }
            }
        }
    }

    /**
     * 将幻灯片转换为图片
     *
     * @param slide 幻灯片对象
     * @param width 图片宽度
     * @param height 图片高度
     * @return 生成的图片对象
     */
    /**
     * 将幻灯片转换为图片 (通用方法，适用于HSLF和XSLF)
     */
//    private static BufferedImage convertSlideToImage(Slide<?, ?> slide, int width, int height) {
//        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
//        Graphics2D graphics = image.createGraphics();
//
//        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
//        graphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
//        graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
//
//        graphics.setColor(Color.WHITE);
//        graphics.fillRect(0, 0, width, height);
//
//        // 获取页面尺寸
//        Dimension2D pgsize = slide.getSlideShow().getPageSize();
//        float scaleX = (float) width / (float) pgsize.getWidth();
//        float scaleY = (float) height / (float) pgsize.getHeight();
//        float scale = Math.min(scaleX, scaleY);
//
//        graphics.scale(scale, scale);
//        slide.draw(graphics);
//        graphics.dispose();
//
//        return image;
//    }

    private static BufferedImage convertSlideToImage(HSLFSlide slide, int width, int height) {
        // 创建图片对象
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D graphics = image.createGraphics();

        // 设置高质量渲染
        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        graphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        graphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);

        // 设置背景色
        graphics.setColor(Color.WHITE);
        graphics.fillRect(0, 0, width, height);

        // 获取PPT原始页面尺寸
        Dimension2D pageSize = slide.getSlideShow().getPageSize();
        double pageWidth = pageSize.getWidth();
        double pageHeight = pageSize.getHeight();

        // 计算适应300x200的最佳缩放比例（保持内容完整且居中）
        double scaleX = width / pageWidth;
        double scaleY = height / pageHeight;
        double scale = Math.min(scaleX, scaleY);

        // 计算居中偏移量
        double offsetX = (width - pageWidth * scale) / 2;
        double offsetY = (height - pageHeight * scale) / 2;



        // 应用缩放和偏移
        graphics.translate(offsetX, offsetY);
        graphics.scale(scale, scale);

//        // POI 4.1.2 中获取页面尺寸的正确方法
//        Dimension pgsize = slide.getSlideShow().getPageSize(); // 通过SlideShow获取
//
//        // 计算缩放比例
//        float scaleX = (float) width / pgsize.width;
//        float scaleY = (float) height / pgsize.height;
//        float scale = Math.min(scaleX, scaleY);
//
//        // 应用缩放
//        graphics.scale(scale, scale);

        // 渲染幻灯片
        slide.draw(graphics);

        // 释放资源
        graphics.dispose();

        return image;
    }

    private static BufferedImage convertSlideToImage2(XSLFSlide slide, int width, int height) {
        // 创建固定尺寸的图片对象
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D graphics = image.createGraphics();

        // 设置高质量渲染参数
        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        graphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        graphics.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        graphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);

        // 设置背景色并填充
        graphics.setColor(Color.WHITE);
        graphics.fillRect(0, 0, width, height);

        // 获取 PPTX 原始页面尺寸（通过 XMLSlideShow 获取）
        Dimension2D pageSize = slide.getSlideShow().getPageSize();
        double pageWidth = pageSize.getWidth();
        double pageHeight = pageSize.getHeight();

        // 计算等比缩放比例（保持内容完整不拉伸）
        double scaleX = width / pageWidth;
        double scaleY = height / pageHeight;
        double scale = Math.min(scaleX, scaleY);

        // 计算居中偏移量（处理宽高比不一致时的留白）
        double offsetX = (width - pageWidth * scale) / 2;
        double offsetY = (height - pageHeight * scale) / 2;

        // 应用变换：先平移（居中）再缩放
        graphics.translate(offsetX, offsetY);
        graphics.scale(scale, scale);

        // 渲染幻灯片内容
        slide.draw(graphics);

        // 释放图形上下文资源
        graphics.dispose();

        return image;
    }

    /**
     * 保存图片到文件
     *
     * @param image        图片对象
     * @param outputFilePath 输出文件路径
     * @throws IOException 如果保存失败
     */
    private static void saveImage(BufferedImage image, String outputFilePath) throws IOException {
        // 获取文件扩展名
        String formatName = outputFilePath.substring(outputFilePath.lastIndexOf(".") + 1);

        // 保存图片
        try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
            ImageIO.write(image, formatName, fos);
        }
    }
}