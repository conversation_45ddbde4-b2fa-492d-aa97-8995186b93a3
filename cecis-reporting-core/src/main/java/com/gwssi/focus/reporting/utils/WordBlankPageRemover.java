package com.gwssi.focus.reporting.utils;

/**
 * <AUTHOR>
 * @Date 2025/6/6 14:59
 * @Description: RemoveBlankPages
 */
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTOnOff;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STOnOff;


import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;

public class WordBlankPageRemover {

    /**
     * 根据页面索引删除指定页面（无论页面内容）
     * @param document 要操作的 XWPFDocument 对象
     * @param pageIndex 要删除的页面索引（从0开始）
     * @throws Exception 异常
     */
    public static void deletePageByIndex(XWPFDocument document, int pageIndex) throws Exception {
        // 步骤1：收集所有分页符的段落索引（页面结束位置）
        List<Integer> pageBreakParagraphIndices = collectPageBreakParagraphIndices(document);
        int totalPages = pageBreakParagraphIndices.size(); // 总页面数 = 分页符数量

        // 校验页面索引有效性
        if (pageIndex < 0 || (pageIndex == totalPages && totalPages > 0)) {
            throw new IllegalArgumentException("页面索引无效，有效范围：0 ~ " + (totalPages - 1));
        }

        // 步骤2：确定要删除的页面的起始和结束段落索引
        int startParaIdx;
        int endParaIdx;

        if (pageIndex == 0) {
            // 第一页：从文档开头到第一个分页符前
            startParaIdx = 0;
            endParaIdx = pageBreakParagraphIndices.get(0);
        } else if (pageIndex == totalPages) {
            // 最后一页：从最后一个分页符后到文档末尾
            startParaIdx = pageBreakParagraphIndices.get(totalPages - 1) + 1;
            endParaIdx = document.getParagraphs().size() - 1;
        } else {
            // 中间页：从前一个分页符后到当前分页符前
            startParaIdx = pageBreakParagraphIndices.get(pageIndex - 1) + 1;
            endParaIdx = pageBreakParagraphIndices.get(pageIndex);
        }

        // 步骤3：删除该页面的所有段落（包括分页符段落）
        deleteParagraphsInRange(document, startParaIdx, endParaIdx);
    }

    /**
     * 收集文档中所有包含分页符的段落索引（页面结束位置）
     */
    private static List<Integer> collectPageBreakParagraphIndices(XWPFDocument document) {
        List<Integer> pageBreakIndices = new ArrayList<>();
        List<XWPFParagraph> paragraphs = document.getParagraphs();

        for (int i = 0; i < paragraphs.size(); i++) {
            XWPFParagraph para = paragraphs.get(i);
            if (isPageBreakParagraph(para)) {
                pageBreakIndices.add(i); // 记录分页符所在的段落索引
            }
        }

        // 处理文档末尾无分页符的情况（最后一个页面）
        if (!pageBreakIndices.isEmpty()) {
            int lastPageBreakIdx = pageBreakIndices.get(pageBreakIndices.size() - 1);
            if (lastPageBreakIdx < paragraphs.size() - 1) {
                pageBreakIndices.add(paragraphs.size() - 1); // 补充最后一个页面的结束位置
            }
        } else {
            // 文档无分页符：只有1页，结束位置为最后一个段落
            pageBreakIndices.add(paragraphs.size() - 1);
        }

        return pageBreakIndices;
    }

    /**
     * 判断段落是否包含分页符（手动或自动）
     */
    private static boolean isPageBreakParagraph(XWPFParagraph para) {
        CTP ctPara = para.getCTP();
        CTPPr pr = ctPara.getPPr();

        // 检查手动分页符（pgBreakAfter）
//        if (pr != null) {
//            STPageBreak pgBreakAfter = pr.getPgBreakAfter();
//            if (pgBreakAfter != null && pgBreakAfter.getVal() == STPageBreak.AFTER) {
//                return true;
//            }
//            STPageBreak pgBreakBefore = pr.getPgBreakBefore();
//            if (pgBreakBefore != null && pgBreakBefore.getVal() == STPageBreak.BEFORE) {
//                return true;
//            }
//        }

        // 检查段落文本是否包含分页符（\f），兼容手动插入的分页符
        String text = para.getText();
        return text.contains("\f");
    }

    /**
     * 删除指定区间内的所有段落（包括起始和结束索引）
     */
    private static void deleteParagraphsInRange(XWPFDocument document, int startIdx, int endIdx) {
        // 从后往前删除段落（避免索引错乱）
        for (int i = endIdx; i >= startIdx; i--) {
            document.removeBodyElement(i); // 删除段落
        }
    }

    // 测试示例
    public static void main(String[] args) {
        try (FileInputStream fis = new FileInputStream("D:\\gwdata\\验厂模型汇报-0603-深圳斯玛顿电气有限公司.docx");
             XWPFDocument document = new XWPFDocument(fis)) {

            // 删除第2页（索引从0开始）
            deletePageByIndex(document, 7);

            // 保存处理后的文档
            try (FileOutputStream fos = new FileOutputStream("D:\\gwdata\\output3.docx")) {
                document.write(fos);
            }
            System.out.println("页面删除成功！");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}