package com.gwssi.focus.reporting.auditreport.impl.service;

import com.gwssi.focus.reporting.auditreport.impl.BusinessProcessing;
import com.gwssi.focus.reporting.utils.DocumentOperator;
import com.gwssi.focus.reporting.utils.StringHandleUtils;
import com.gwssi.focus.reporting.utils.WordProcessorUtil;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.stereotype.Component;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/6/6 15:32
 * @Description: AbnormalInvoiceExcel
 */
@Component
public class AbnormalInvoiceExcel implements BusinessProcessing {


    @Override
    public boolean process(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        XWPFDocument document = (XWPFDocument)parameters.get("document");
        List<Map> mapsNH = processNH(table, parameters);
        DocumentOperator.replaceWordText(document,"${counterfeitingEnergyConsumption}", mapsNH.isEmpty()?"通过":"不通过");
        List<Map> mapsXX = processXX(table, parameters);
        DocumentOperator.replaceWordText(document,"${counterfeitingOutput}",  mapsXX.isEmpty()?"通过":"不通过");
        List<Map> mapsJX = processXX(table, parameters);
        DocumentOperator.replaceWordText(document,"${counterfeitingInput}", mapsJX.isEmpty()?"通过":"不通过");
        String placeholder = (String)parameters.get("placeholder");
        String wordPath = (String)parameters.get("wordPath");
        Integer sourceTableIndex = (Integer)parameters.get("sourceTableIndex");
        if (!mapsNH.isEmpty() || !mapsXX.isEmpty() || !mapsJX.isEmpty()){
            try (FileInputStream sourceFis = new FileInputStream(wordPath);
                 XWPFDocument sourceDoc = new XWPFDocument(sourceFis)) {
                // 获取源表格
                XWPFTable sourceTable = sourceDoc.getTables().get(sourceTableIndex);

                // 查找占位符段落
                XWPFParagraph placeholderPara = DocumentOperator.findParagraphByText(document, placeholder);
                if (placeholderPara == null) {
                    throw new IllegalArgumentException("未找到占位符: " + placeholder);
                }
                // 创建新表格并复制内容
                XWPFTable newTable = placeholderPara.getDocument().insertNewTbl(placeholderPara.getCTP().newCursor());
                DocumentOperator.copyTableContent(sourceTable, newTable);
                int num = 1;
                // 插入数据
                num = insertData(newTable, mapsNH, num);
                num = insertData(newTable, mapsXX, num);
                insertData(newTable, mapsJX, num);
                // 把提示文字文字添加进去
                String stringTmp = "建议：\n" +
                        "1. 确认发票异常原因\n" +
                        "\t•\t核查发票号、发票代码、开票日期是否填写有误；\n" +
                        "\t•\t与工厂确认是否为作废发票或测试用票误提交；\n" +
                        "\t•\t如为重复票据，需查清是否属同一合同或供应商多开。\n" +
                        "2. 要求补充合法合规的发票\n" +
                        "\t•\t要求工厂提供相同交易的替代有效发票；\n" +
                        "\t•\t如无替代发票，需说明采购方式及支付路径，并补充合同、付款凭证、收料单等佐证材料；\n" +
                        "\t•\t对于“挂靠采购”或“集团代开”情形，要求提供委托协议及内部往来记录。\n" +
                        "3. 穿透调查相关供应商\n" +
                        "\t•\t对于异常发票对应的供应商，应进一步核查其：\n" +
                        "\t•\t是否正常经营（可结合天眼查、发票流分析）；\n" +
                        "\t•\t是否存在与工厂法人、股东、财务人员有关系交叉；\n" +
                        "\t•\t是否为新设立企业、小规模纳税人、高风险行业公司。\n" +
                        "4. 加强本次验厂处理意见\n" +
                        "\t•\t如异常发票金额较大、比例较高，建议：\n" +
                        "\t•\t验厂结果列为“不通过”或“需复审”；\n" +
                        "\t•\t标记为重点监控对象，限制退税服务资格；\n" +
                        "\t•\t如异常为少量、可修复，可临时列为“整改项”，要求限期补票。\n";
                List<String> list = Arrays.asList(stringTmp.split("\n"));
                DocumentOperator.replacePlaceholders(document,"${abnormalInvoiceSuggestion}",list);
                // 移除占位符文本
                DocumentOperator.clearParagraphText(placeholderPara);
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            return true;
        }else {
            DocumentOperator.deleteParagraphByText(document,"验伪不通过的发票明细：");
            DocumentOperator.deleteParagraphByText(document,"${abnormalInvoiceExcel}");
            DocumentOperator.deleteParagraphByText(document,"${abnormalInvoiceSuggestion}");
            return false;
        }
    }

    public Integer insertData(XWPFTable table, List<Map> maps,Integer num) {
        for (int i = 0; i < maps.size(); i++) {
            XWPFTableRow row = WordProcessorUtil.createRow(table);
            row.getCell(0).setText(String.valueOf(num++));
            row.getCell(1).setText(String.valueOf(maps.get(i).get("invoiceCode")));
            row.getCell(2).setText(String.valueOf(maps.get(i).get("invoiceNumber")));
            row.getCell(3).setText(String.valueOf(maps.get(i).get("digitalTicketNumber")));
            row.getCell(4).setText(String.valueOf(maps.get(i).get("sellerIdentificationNumber")));
            row.getCell(5).setText(String.valueOf(maps.get(i).get("sellerName")));
            row.getCell(6).setText(String.valueOf(maps.get(i).get("purchaserIdentificationNumber")));
            row.getCell(7).setText(String.valueOf(maps.get(i).get("purchaserName")));
            row.getCell(8).setText(String.valueOf(maps.get(i).get("invoiceDateStr")));
            row.getCell(9).setText(maps.get(i).get("amount") == null?"":StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("amount"))));
            row.getCell(10).setText(maps.get(i).get("taxAmount") == null?"":StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("taxAmount"))));
            row.getCell(11).setText(maps.get(i).get("totalAmountIncludingTax") == null?"":StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("totalAmountIncludingTax"))));
            row.getCell(12).setText(String.valueOf(maps.get(i).get("invoiceSource")));
            row.getCell(13).setText(String.valueOf(maps.get(i).get("invoiceType")));
            row.getCell(14).setText(String.valueOf(maps.get(i).get("invoiceStatus")));
            row.getCell(15).setText(String.valueOf(maps.get(i).get("invoiceRiskLevel")));
            row.getCell(16).setText(String.valueOf(maps.get(i).get("drawer")));
            row.getCell(17).setText(String.valueOf(maps.get(i).get("remarks")));
        }
        return num;
    }

    // 能耗
    public List<Map> processNH(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        IPersistenceDAO daoODS = DAOManager.getPersistenceDAO("dataSource_DB_ODS");
        SqlHelper sql = new SqlHelper();
        sql.append("select t.*,TO_CHAR(invoice_date, 'YYYY-MM-DD') as invoice_date_str from ods_input_invoice t where purchaser_name = ? and (goods_taxable_services like '%电费%' or goods_taxable_services like '%电服务费%' or goods_taxable_services like '%水费%' or goods_taxable_services like '%水服务费%') and verification_result = 1", parameters.get("company"));
        List<Map> maps = daoODS.queryForList(sql.getSql(), sql.getParamList());
        return maps;
    }


    // 销项
    public List<Map> processXX(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        IPersistenceDAO daoODS = DAOManager.getPersistenceDAO("dataSource_DB_ODS");
        SqlHelper sql = new SqlHelper();
        sql.append("select t.*,TO_CHAR(invoice_date, 'YYYY-MM-DD') as invoice_date_str from ods_output_invoices t where seller_name = ? and verification_result = 1",parameters.get("company"));
        List<Map> maps = daoODS.queryForList(sql.getSql(), sql.getParamList());
        return maps;
    }

    // 进项
    public List<Map> processJX(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        IPersistenceDAO daoODS = DAOManager.getPersistenceDAO("dataSource_DB_ODS");
        SqlHelper sql = new SqlHelper();
        sql.append("select t.*,TO_CHAR(invoice_date, 'YYYY-MM-DD') as invoice_date_str from ods_input_invoice t where purchaser_name = ? and goods_taxable_services not like '%电费%' and goods_taxable_services not like '%电服务费%' and goods_taxable_services not like '%水费%' and goods_taxable_services not like '%水服务费%' and verification_result = 1",parameters.get("company"));
        List<Map> maps = daoODS.queryForList(sql.getSql(), sql.getParamList());
        return maps;
    }
}
