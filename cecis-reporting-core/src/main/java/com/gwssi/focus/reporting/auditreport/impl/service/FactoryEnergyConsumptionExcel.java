package com.gwssi.focus.reporting.auditreport.impl.service;

import com.gwssi.focus.reporting.auditreport.impl.BusinessProcessing;
import com.gwssi.focus.reporting.utils.StringHandleUtils;
import com.gwssi.focus.reporting.utils.WordProcessorUtil;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/21 16:31
 * @Description: 按月展示工厂能耗信息
 */
@Component
public class FactoryEnergyConsumptionExcel implements BusinessProcessing {

    @Override
    public boolean process(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        IPersistenceDAO daoDWD = DAOManager.getPersistenceDAO("dataSource_DB_DWD");
        SqlHelper sql = new SqlHelper();
        sql.append("select * from DWD_FACTORY_INSPECTION_ENERGY_ONSUMPTION where name = ? ",parameters.get("company"));
        List<Map> maps = daoDWD.queryForList(sql.getSql(), sql.getParamList());
        if (maps.isEmpty()){
            return false;
        }
        for (int i = 0; i < maps.size(); i++) {
            XWPFTableRow row = WordProcessorUtil.createRow(table);
            row.getCell(0).setText(StringHandleUtils.convertToChineseMonthFormat(String.valueOf(maps.get(i).get("statYearMonth"))));
            row.getCell(1).setText("电");
            row.getCell(2).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("electricityConsumption"))));
            row.getCell(3).setText("kw/h");
            row.getCell(4).setText(StringHandleUtils.formatNumber(String.valueOf(maps.get(i).get("electricityAmount"))));
        }
        return true;
    }

}