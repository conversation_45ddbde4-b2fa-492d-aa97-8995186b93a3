package com.gwssi.focus.reporting;


import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2025/5/20 9:48
 * @Description: tablecopyTest
 */
public class tablecopyTest {


    /**
     * 根据占位符文本定位并插入表格
     */
    public static void copyTableUsingPlaceholder(
            String sourceDocPath,
            String targetDocPath,
            String outputPath,
            int sourceTableIndex,
            String placeholderText) throws IOException {

        try (FileInputStream sourceFis = new FileInputStream(sourceDocPath);
             XWPFDocument sourceDoc = new XWPFDocument(sourceFis);
             FileInputStream targetFis = new FileInputStream(targetDocPath);
             XWPFDocument targetDoc = new XWPFDocument(targetFis)) {

            // 获取源表格
            XWPFTable sourceTable = sourceDoc.getTables().get(sourceTableIndex);

            // 查找占位符段落
            XWPFParagraph placeholderPara = findParagraphByText(targetDoc, placeholderText);
            if (placeholderPara == null) {
                throw new IllegalArgumentException("未找到占位符: " + placeholderText);
            }

            // 创建新表格并复制内容
            XWPFTable newTable = insertTableAfterParagraph(targetDoc, placeholderPara);
//            copyTable(sourceTable, newTable);
            copyTableContent(sourceTable, newTable);

            // 移除占位符文本
            clearParagraphText(placeholderPara);

            // 保存目标文档
            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                targetDoc.write(fos);
            }
        }
    }

    /**
     * 在指定段落之后插入表格
     */
    /**
     * 在指定段落之后插入表格（最终修正版）
     */
    private static XWPFTable insertTableAfterParagraph(XWPFDocument doc, XWPFParagraph paragraph) {
//        // 获取段落的CTP对象
//        CTP ctp = paragraph.getCTP();
//
//        // 创建新表格并正确关联到文档
//        CTTbl newCTTbl = doc.getDocument().getBody().addNewTbl();
//
//        // 创建表格属性
//        CTTblPr tblPr = newCTTbl.addNewTblPr();
//        tblPr.addNewTblW().setW(BigInteger.valueOf(5000)); // 默认宽度
//
//        // 在段落之后插入新表格的XML
//        ctp.getDomNode().getParentNode().insertBefore(
//                newCTTbl.getDomNode(),
//                ctp.getDomNode().getNextSibling()
//        );
//
//        // 创建XWPFTable对象
//        XWPFTable newTable = new XWPFTable(newCTTbl, doc);
//
//        // 确保表格至少有一行
//        if (newTable.getRows().isEmpty()) {
//            newTable.createRow();
//        }
        XWPFTable table = paragraph.getDocument().insertNewTbl(paragraph.getCTP().newCursor());

        return table;
    }



    /**
     * 复制表格内容（修正版）
     */
    private static void copyTableContent(XWPFTable sourceTable, XWPFTable targetTable) {
        // 复制表格属性
        CTTblPr sourceTblPr = sourceTable.getCTTbl().getTblPr();
        if (sourceTblPr != null) {
            targetTable.getCTTbl().setTblPr((CTTblPr) sourceTblPr.copy());
        }

        // 复制行和单元格
        for (int i = 0; i < sourceTable.getRows().size(); i++) {
            XWPFTableRow sourceRow = sourceTable.getRow(i);
            XWPFTableRow targetRow = (i == 0) ? targetTable.getRow(0) : targetTable.createRow();

            // 复制行属性
            CTTrPr sourceTrPr = sourceRow.getCtRow().getTrPr();
            if (sourceTrPr != null) {
                targetRow.getCtRow().setTrPr((CTTrPr) sourceTrPr.copy());
            }

            // 确保目标行有足够的单元格
            while (targetRow.getTableCells().size() < sourceRow.getTableCells().size()) {
                targetRow.addNewTableCell();
            }

            // 复制单元格内容和样式
            for (int j = 0; j < sourceRow.getTableCells().size(); j++) {
                XWPFTableCell sourceCell = sourceRow.getCell(j);
                XWPFTableCell targetCell = targetRow.getCell(j);

                // 复制单元格属性
                CTTcPr sourceTcPr = sourceCell.getCTTc().getTcPr();
                if (sourceTcPr != null) {
                    targetCell.getCTTc().setTcPr((CTTcPr) sourceTcPr.copy());
                }

                // 清空目标单元格（修正版）
                clearCellContent(targetCell);

                // 复制段落和文本
                for (XWPFParagraph sourcePara : sourceCell.getParagraphs()) {
                    XWPFParagraph targetPara = targetCell.addParagraph();
                    copyParagraphContent(sourcePara, targetPara);
                }
            }
        }
    }

    /**
     * 清空单元格内容（不使用clear()方法）
     */
    private static void clearCellContent(XWPFTableCell cell) {
        // 移除所有段落，只保留一个空段落
//        while (cell.getParagraphs().size() > 1) {
//            cell.removeParagraph(1);
//        }
        while (cell.getParagraphs().size() > 0) {
            cell.removeParagraph(0);
        }

        // 清空保留的段落
//        XWPFParagraph para = cell.getParagraphs().get(0);
//        while (para.getRuns().size() > 0) {
//            para.removeRun(0);
//        }
//
//        // 添加一个空运行以维持段落结构
//        para.createRun();
    }
    /**
     * 复制段落内容
     */
    private static void copyParagraphContent(XWPFParagraph sourcePara, XWPFParagraph targetPara) {
        // 复制段落属性
        CTPPr sourcePPr = sourcePara.getCTP().getPPr();
        if (sourcePPr != null) {
            targetPara.getCTP().setPPr((CTPPr) sourcePPr.copy());
        }

        // 复制运行元素
//        targetPara.getRuns().clear();
        for (XWPFRun sourceRun : sourcePara.getRuns()) {
            XWPFRun targetRun = targetPara.createRun();

            // 复制运行属性
            CTRPr sourceRPr = sourceRun.getCTR().getRPr();
            if (sourceRPr != null) {
                targetRun.getCTR().setRPr((CTRPr) sourceRPr.copy());
            }
            // 复制文本
            targetRun.setText(sourceRun.getText(0), 0);
        }
    }

    // 复制整个表格
    public static void copyTable(XWPFTable sourceTable, XWPFTable targetTable) {
        for (int i = 0; i < sourceTable.getNumberOfRows(); i++) {
            XWPFTableRow sourceRow = sourceTable.getRow(i);
            XWPFTableRow targetRow = targetTable.createRow();

            for (int j = 0; j < sourceRow.getTableCells().size(); j++) {
                XWPFTableCell sourceCell = sourceRow.getCell(j);
                XWPFTableCell targetCell = targetRow.createCell();

                // 关键修复：通过循环删除目标单元格的段落
                while (targetCell.getParagraphs().size() > 0) {
                    targetCell.removeParagraph(0);
                }

                // 复制源单元格内容到目标
                for (XWPFParagraph sourcePara : sourceCell.getParagraphs()) {
                    XWPFParagraph targetPara = targetCell.addParagraph();
                    copyParagraphContent(sourcePara, targetPara);
                }
            }
        }
    }




    /**
     * 根据文本内容查找段落
     */
    private static XWPFParagraph findParagraphByText(XWPFDocument doc, String text) {
        for (XWPFParagraph para : doc.getParagraphs()) {
            if (para.getText().contains(text)) {
                return para;
            }
        }
        return null;
    }

    /**
     * 清除段落文本
     */
    private static void clearParagraphText(XWPFParagraph para) {
        for (int i = para.getRuns().size() - 1; i >= 0; i--) {
            para.removeRun(i);
        }
        para.createRun().setText(""); // 保留空运行以维持段落结构
    }
    public static void main(String[] args) {
        try {
            // 方案1：使用占位符
            copyTableUsingPlaceholder(
                    "D:\\gwdata\\验厂模型汇报-0519沟通.docx",
                    "D:\\gwdata\\验厂模型汇报-0515.docx",
                    "D:\\gwdata\\验厂模型汇报-0515完.docx",
                    0,
                    "${productTable}"
            );

//            // 方案2：根据内容特征（在标题"表格插入位置"后插入）
//            WordTableCopier.copyTableByContent(
//                    "source.docx",
//                    "target.docx",
//                    "output.docx",
//                    0,
//                    "表格插入位置",
//                    true // 插入到匹配内容之后
//            );
//
//            // 方案3：根据样式（在所有一级标题后插入表格）
//            WordTableCopier.copyTableByStyle(
//                    "source.docx",
//                    "target.docx",
//                    "output.docx",
//                    0,
//                    "Heading1" // 一级标题样式ID
//            );
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
