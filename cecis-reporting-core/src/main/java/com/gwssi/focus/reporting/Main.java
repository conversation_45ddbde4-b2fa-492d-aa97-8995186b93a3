package com.gwssi.focus.reporting;

import java.io.IOException;

//TIP To <b>Run</b> code, press <shortcut actionId="Run"/> or
// click the <icon src="AllIcons.Actions.Execute"/> icon in the gutter.
public class Main {
    public static void main(String[] args) {
        try {
            // 示例1：从Excel导入表格到Word
            TableProcessor processor = new TableProcessor("D:\\gwdata\\验厂模型汇报-0515.docx");
            processor.importExcelTable("D:\\gwdata\\excel.xlsx", "Sheet1", "${excelData}");

            // 示例2：从Word导入表格到另一个Word
//            processor.importWordTable("source.docx", "${sourceTable}", "${targetTable}");


//            // 示例3：修改表格（添加行）
//            processor.modifyTable("${tableToModify}", table -> {
//                XWPFTableRow newRow = table.createRow();
//                newRow.getCell(0).setText("New Row");
//                newRow.getCell(1).setText("Data");
//            });
//
//            // 示例4：合并单元格
//            processor.modifyTable("${tableToModify}", table -> {
//                // 合并第2行的第1-2列
//                table.getRow(1).getCell(0).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
//                table.getRow(1).getCell(1).getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
//            });

            // 保存文档
            processor.saveDocument("D:\\gwdata\\验厂模型汇报-0515完.docx");

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}