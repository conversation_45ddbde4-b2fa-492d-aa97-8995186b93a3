package com.gwssi.focus.reporting.inquiryreport.service;




import com.gwssi.focus.reporting.auditreport.impl.WordProcessorUtilFactory;
import com.gwssi.focus.reporting.auditreport.impl.service.*;
import com.gwssi.focus.reporting.common.DocumentBuilder;
import com.gwssi.focus.reporting.utils.DocumentOperator;
import com.gwssi.focus.reporting.utils.PptProcessorUtil;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.service.BaseService;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.*;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class FactoryInquiryService extends BaseService {
    @Autowired
    private EnergyConsumptionAndShipmentsCensorExcel energyConsumptionAndShipmentsCensorExcel;
    @Autowired
    private EnergyConsumptionInvoiceExcel energyConsumptionInvoiceExcel;
    @Autowired
    private FactoryEnergyConsumptionExcel factoryEnergyConsumptionExcel;
    @Autowired
    private FreezeEntryExcel freezeEntryExcel;
    @Autowired
    private InputInvoiceExcel inputInvoiceExcel;
    @Autowired
    private InputProductsAnalyseExcel inputProductsAnalyseExcel;
    @Autowired
    private OutputInvoiceExcel outputInvoiceExcel;
    @Autowired
    private ScreeningResultsExcel screeningResultsExcel;
    @Autowired
    private Top10InputMaterialExcel top10InputMaterialExcel;
    @Autowired
    private Top10OutputProductExcel top10OutputProductExcel;
    @Autowired
    private Top10ProviderExcel top10ProviderExcel;
    @Autowired
    private AbnormalInvoiceExcel abnormalInvoiceExcel;
    @Autowired
    private WordProcessorUtilFactory factory;


    private static final String AUDIT_COMPANY_NAME = "深圳中电投资有限公司";

    private void setCellFormat(XWPFTableCell cell, int fontSize, String fontFamily) {
        // 1. 获取或创建段落（每个单元格默认有一个段落，若不存在则创建）
        XWPFParagraph paragraph = cell.getParagraphs().isEmpty() ?
                cell.addParagraph() : cell.getParagraphs().get(0);

        // 2. 获取或创建运行（每个段落至少有一个运行）
        XWPFRun run = paragraph.getRuns().isEmpty() ?
                paragraph.createRun() : paragraph.getRuns().get(0);

        // 3. 设置字体格式
        run.setFontSize(fontSize); // 字号（磅）
        run.setFontFamily(fontFamily); // 字体
        run.setBold(false); // 非加粗

        // 4. 设置段落对齐方式（可选：左对齐、居中、右对齐）
        paragraph.setAlignment(ParagraphAlignment.LEFT);

        // 5. 设置单元格垂直对齐（可选：居中）
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }
    public void generateReport(String companyName,String auditReportDate) throws OptimusException, IOException, InvalidFormatException {
//        ZipSecureFile.setMinInflateRatio(0.005);
//         模板路径和输出路径
        String currentTimeStr = getCurrentTimeStr();
        String wordTemplatePath = "D:\\gwdata\\123.docx";
        String pptTemplatePath = "D:\\gwdata\\ppt模板tiger.pptx";
        String excelTemplatePath = "D:\\gwdata\\template\\全部模板.docx";
        IPersistenceDAO daoDWD = getPersistenceDAO("dataSource_DB_DWD");
        IPersistenceDAO daoODS = getPersistenceDAO("dataSource_DB_ODS");
        IPersistenceDAO daoADS = DAOManager.getPersistenceDAO("dataSource_DB_ADS");
        SqlHelper sql = new SqlHelper().append("select * from DWD_FACTORY_INSPECTION_DETAILS where 1=1 ");
        sql.appendIfNotBlank(" and name = ? ", companyName);
//        sql.append(" and usc_code in ('914403003266972313','91440300MA5EWJCNX4','91440300MA5FL5WQ4A','91440300MA5G6CWK3T','91440300MA5HFQ7D5J','91441900MA51AAD44E') ");
        List<Map> maps = daoDWD.queryForList(sql.getSql(), sql.getParamList());
        for (int i = 0; i < maps.size(); i++) {
            String outputPathTmp = "D:\\gwdata\\OUT\\验厂模型汇报-"+currentTimeStr+"-"+maps.get(i).get("name")+"-tmp.docx";
            String outputPath = "D:\\gwdata\\OUT\\验厂模型汇报-"+currentTimeStr+"-"+maps.get(i).get("name")+".docx";
            String outputPathJPG = "D:\\gwdata\\OUT\\验厂模型汇报-"+currentTimeStr+"-"+maps.get(i).get("name")+"-ppt.jpg";
            String outputPathPDF = "D:\\gwdata\\OUT\\验厂模型汇报-"+currentTimeStr+"-"+maps.get(i).get("name")+".pdf";
            // 从工厂获取实例并确保初始化
            XWPFDocument document = DocumentBuilder.build(wordTemplatePath);
            Map<String, Object> tmp = new HashMap<>();
            tmp.put("company",String.valueOf(maps.get(i).get("name")));
            tmp.put("code",String.valueOf(maps.get(i).get("uscCode")));
            tmp.put("auditReportDate",auditReportDate);


            // 给表格增加内容
            DocumentOperator.changeWordTable(document,3,top10ProviderExcel,tmp);
            DocumentOperator.changeWordTable(document,5,inputProductsAnalyseExcel,tmp);
            DocumentOperator.changeWordTable(document,7,factoryEnergyConsumptionExcel,tmp);
            DocumentOperator.changeWordTable(document,8,energyConsumptionAndShipmentsCensorExcel,tmp);
            boolean flag = DocumentOperator.importWordTable(document, "${abnormalInvoiceExcel}", excelTemplatePath, 8, abnormalInvoiceExcel, tmp);
//            DocumentOperator.importWordTable(document,5,outputInvoiceExcel,tmp);
//            DocumentOperator.importWordTable(document,5,energyConsumptionInvoiceExcel,tmp);
            // 替换文本
            // 基本信息
            DocumentOperator.replaceWordText(document,"${companyName}", String.valueOf(maps.get(i).get("name")));
            DocumentOperator.replaceWordText(document,"${reportDate}", getData());
            DocumentOperator.replaceWordText(document,"${reportYear}", getYear());
            DocumentOperator.replaceWordText(document,"${reportRandom}", generate5DigitString());
            DocumentOperator.replaceWordText(document,"${companySocial}", String.valueOf(maps.get(i).get("uscCode")));
            DocumentOperator.replaceWordText(document,"${taskNumber}", String.valueOf(maps.get(i).get("number")));
            DocumentOperator.replaceWordText(document,"${auditReportDate}", changeData(auditReportDate));
            DocumentOperator.replaceWordText(document,"${auditCompanyName}", AUDIT_COMPANY_NAME);

            sql.clear();
            sql.append(" select case when r.INDICATOR_VALUE > 0 then '1' else '0' end as INDICATOR_VALUE_STR,r.REMARKS as RREMARKS ,i.* " +
                    " from ADS_FACTORY_INSPECTION_RESULTS r " +
                    " left join ADS_INDICATOR i on i.INDICATOR_NUMBER = r.INDICATOR_NUMBER " +
                    " where r.usc_code = ? and r.number = ?", String.valueOf(maps.get(i).get("uscCode")),String.valueOf(maps.get(i).get("number")));
            List<Map> maps1 = daoADS.queryForList(sql.getSql(), sql.getParamList());
            Map<String, Map> indicator = maps1.stream().collect(Collectors.toMap(
                    // 从每个Map中提取"id"字段作为键
                    map -> (String) map.get("indicatorNumber"),
                    // 值就是Map本身
                    map -> map,
                    // 处理键冲突：保留新值
                    (existing, replacement) -> replacement
            ));
            // 验厂结论
            DocumentOperator.replaceWordText(document,"${conclusion}", "0".equals(indicator.get("A0001").get("indicatorValueStr"))?"通过":"不通过");
            // 验厂结论明细
            DocumentOperator.replaceWordText(document,"${A0006}", generateResult(indicator.get("A0006"),true));
            DocumentOperator.replaceWordText(document,"${A0007}", generateResult(indicator.get("A0007"),true));
            DocumentOperator.replaceWordText(document,"${A0009}", generateResult(indicator.get("A0009"),true));
            DocumentOperator.replaceWordText(document,"${A0019}", generateResult(indicator.get("A0019"),true));
            DocumentOperator.replaceWordText(document,"${A0020}", generateResult(indicator.get("A0020"),true));
            DocumentOperator.replaceWordText(document,"${A0021}", generateResult(indicator.get("A0021"),true));
            DocumentOperator.replaceWordText(document,"${A0022}", generateResult(indicator.get("A0022"),true));
            DocumentOperator.replaceWordText(document,"${A0023}", generateResult(indicator.get("A0023"),true));
            DocumentOperator.replaceWordText(document,"${A0024}", generateResult(indicator.get("A0024"),true));
            DocumentOperator.replaceWordText(document,"${A0025}", generateResult(indicator.get("A0025"),true));
            DocumentOperator.replaceWordText(document,"${A0026}", generateResult(indicator.get("A0026"),true));
            DocumentOperator.replaceWordText(document,"${A0013}", generateResult(indicator.get("A0013"),false));
            DocumentOperator.replaceWordText(document,"${A0027}", generateResult(indicator.get("A0027"),false));
            DocumentOperator.replaceWordText(document,"${A0028}", generateResult(indicator.get("A0028"),false));
            DocumentOperator.replaceWordText(document,"${A0029}", generateResult(indicator.get("A0029"),false));
            DocumentOperator.replaceWordText(document,"${A0030}", generateResult(indicator.get("A0030"),false));
            DocumentOperator.replaceWordText(document,"${A0015}", generateResult(indicator.get("A0015"),true));
            DocumentOperator.replaceWordText(document,"${A0016}", "0".equals(indicator.get("A0016").get("indicatorValueStr"))?"进销项比例合理":"进销项比例不合理");
            DocumentOperator.replaceWordText(document,"${A0018}", "0".equals(indicator.get("A0018").get("indicatorValueStr"))?"SGR\\EGR比例合理":"SGR\\EGR比例不合理");


            // 处理ppt
            PptProcessorUtil.processFile(pptTemplatePath, outputPathJPG, indicator, Color.RED);
            // 替换图片
            DocumentOperator.replaceImage(document,"${modelResultPng}", outputPathJPG, 420, 280);
            // 保存文档
            DocumentOperator.saveDocument(document,outputPathTmp);
            System.out.println("完成");

            List<Integer> tableIndexs = new ArrayList<>();
            if (flag){
                tableIndexs.add(3);
                tableIndexs.add(5);
                tableIndexs.add(6);
                tableIndexs.add(8);
                tableIndexs.add(9);
            }else {
                tableIndexs.add(3);
                tableIndexs.add(5);
                tableIndexs.add(7);
                tableIndexs.add(8);
            }
            // 修改表格字体
            DocumentOperator.changeWordTableStyle(outputPathTmp,outputPath,tableIndexs,9);

//            WordToPdfConverter.convertWordToPdf1(outputPath,outputPathPDF);
//            WordToPdfConverter.convertWordToPdf1("D:\\gwdata\\OUT\\验厂模型汇报-2025-06-09-深圳斯玛顿电气有限公司.docx","D:\\gwdata\\OUT\\验厂模型汇报-2025-06-09-深圳斯玛顿电气有限公司.pdf");
        }

    }
    public static String getCurrentTimeStr() {
        return getCurrentTimeStr("yyyy-MM-dd");
    }

    /**
     * 获取当前时间字符串（自定义格式）
     * @param pattern 时间格式（如：yyyy/MM/dd HH:mm）
     * @return 格式化后的时间字符串
     */
    public static String getCurrentTimeStr(String pattern) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        return LocalDateTime.now().format(formatter);
    }

    private static String changeData(String inputDate){

        // 定义输入日期格式
        DateTimeFormatter inputFormatter = DateTimeFormatter.ISO_LOCAL_DATE;

        // 定义输出日期格式（中文年月日格式）
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日", Locale.CHINA);

        // 解析输入字符串为 LocalDate
        LocalDate date = LocalDate.parse(inputDate, inputFormatter);

        // 格式化为目标字符串
        String formattedDate = date.format(outputFormatter);
        return formattedDate;
    }

    private static String generateResult(Map map,boolean isSplice){
        String result = "";
        if ("0".equals(map.get("indicatorValueStr"))){
            result += "【通过】";
            if (isSplice){
                if (StringUtils.isBlank((String)map.get("rremarks"))){
                    result += map.get("passingStandard");
                }else {
                    result += map.get("rremarks");
                }
            }
        }else {
            if (isSplice){
                result += "【不通过】";
                if (StringUtils.isBlank((String)map.get("rremarks"))){
                    result += map.get("failurePassStandard");
                }else {
                    result += map.get("rremarks");
                }
            }
        }
        return result;
    }

    public static String getData() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 定义格式化模式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年M月d日", Locale.CHINA);
        // 格式化日期
        String formattedDate = currentDate.format(formatter);
        return formattedDate;
    }
    public static String getYear() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 提取年份（返回int类型）
        int year = currentDate.getYear();
        // 转换为字符串（如需字符串格式）
        String yearStr = String.valueOf(year);
        return yearStr;
    }

    public static String generate5DigitString() {
        Random random = new Random();
        // 生成0-99999之间的随机数
        int randomNum = random.nextInt(100000);
        // 格式化为5位字符串，不足前补零
        return String.format("%05d", randomNum);
    }

}
