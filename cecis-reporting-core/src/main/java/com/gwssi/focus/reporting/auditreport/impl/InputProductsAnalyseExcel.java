package com.gwssi.focus.reporting.auditreport.impl;

import com.gwssi.focus.reporting.utils.BigDecimalUtils;
import com.gwssi.focus.reporting.utils.WordProcessorUtil;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/21 16:31
 * @Description: 主要产品进项结构化分析
 */
@Component
public class InputProductsAnalyseExcel implements BusinessProcessing {

    @Override
    public boolean process(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        IPersistenceDAO daoDWS = DAOManager.getPersistenceDAO("DB_DWS");
        SqlHelper sql = new SqlHelper();
        sql.append("select * from dws_input_goods_class t where usc_code = ? ",parameters.get("code"));
        List<Map> maps = daoDWS.queryForList(sql.getSql(), sql.getParamList());
        if (maps.isEmpty()){
            return false;
        }
        for (int i = 0; i < maps.size(); i++) {
            XWPFTableRow row = table.createRow();
            row.getCell(0).setText(String.valueOf(i+1));
            row.getCell(1).setText(String.valueOf(maps.get(i).get("categoryName")));
            row.getCell(2).setText(String.valueOf(maps.get(i).get("itemName")));
            row.getCell(3).setText(maps.get(i).get("industryBegin")+"-"+maps.get(i).get("industryEnd"));
            row.getCell(4).setText(String.valueOf(maps.get(i).get("actual")));
            row.getCell(5).setText(String.valueOf(maps.get(i).get("promptContent")));
        }
        return true;
    }
}