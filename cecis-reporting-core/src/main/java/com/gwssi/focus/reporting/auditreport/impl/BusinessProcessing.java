package com.gwssi.focus.reporting.auditreport.impl;

/**
 * <AUTHOR>
 * @Date 2025/5/21 16:24
 * @Description: ReportProcessor
 */

import com.gwssi.optimus.core.exception.OptimusException;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public interface BusinessProcessing {
    boolean process(XWPFTable table, Map<String, Object> parameters) throws OptimusException;

}