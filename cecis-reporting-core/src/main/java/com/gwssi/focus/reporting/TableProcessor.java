package com.gwssi.focus.reporting;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.io.*;

public class TableProcessor {
    private XWPFDocument targetDocument;

    public TableProcessor(String targetDocPath) throws IOException {
        this.targetDocument = new XWPFDocument(new FileInputStream(targetDocPath));
    }

    // 从Excel导入表格并插入到Word
    public void importExcelTable(String excelPath, String sheetName, String placeholder) throws IOException {
        try (FileInputStream fis = new FileInputStream(excelPath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                throw new IllegalArgumentException("Sheet not found: " + sheetName);
            }

            // 创建Word表格
            XWPFTable table = createTableFromExcelSheet(sheet);

            // 插入到目标文档
            insertTableAfterPlaceholder(table, placeholder);
        }
    }

    // 从Word导入表格并插入到另一个Word
    public void importWordTable(String sourceDocPath, String placeholderInSource, String placeholderInTarget) throws IOException {
        try (FileInputStream fis = new FileInputStream(sourceDocPath);
             XWPFDocument sourceDoc = new XWPFDocument(fis)) {

            // 查找源文档中的表格
            XWPFTable sourceTable = findTableAfterPlaceholder(sourceDoc, placeholderInSource);
            if (sourceTable == null) {
                throw new IllegalArgumentException("Table not found after placeholder: " + placeholderInSource);
            }

            // 克隆表格
            XWPFTable clonedTable = cloneTable(sourceTable);

            // 插入到目标文档
            insertTableAfterPlaceholder(clonedTable, placeholderInTarget);
        }
    }

    // 从Excel工作表创建Word表格
    private XWPFTable createTableFromExcelSheet(Sheet sheet) {
        XWPFTable table = targetDocument.createTable();

        // 处理表头
        Row headerRow = sheet.getRow(0);
        XWPFTableRow wordHeaderRow = table.getRow(0);

        for (int i = 0; i < headerRow.getLastCellNum(); i++) {
            if (i >= wordHeaderRow.getTableCells().size()) {
                wordHeaderRow.addNewTableCell();
            }
            wordHeaderRow.getCell(i).setText(headerRow.getCell(i).getStringCellValue());
        }

        // 处理数据行
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row excelRow = sheet.getRow(i);
            XWPFTableRow wordRow = table.createRow();

            for (int j = 0; j < excelRow.getLastCellNum(); j++) {
                Cell cell = excelRow.getCell(j);
                if (cell != null) {
                    wordRow.getCell(j).setText(getCellValueAsString(cell));
                }
            }
        }

        return table;
    }

    // 克隆Word表格
    private XWPFTable cloneTable(XWPFTable sourceTable) {
        XWPFTable targetTable = targetDocument.createTable();

        // 复制表格样式
        CTTblPr sourceTblPr = sourceTable.getCTTbl().getTblPr();
        if (sourceTblPr != null) {
            targetTable.getCTTbl().setTblPr((CTTblPr) sourceTblPr.copy());
        }

        // 复制行和单元格
        for (int i = 0; i < sourceTable.getRows().size(); i++) {
            XWPFTableRow sourceRow = sourceTable.getRow(i);
            XWPFTableRow targetRow = i == 0 ? targetTable.getRow(0) : targetTable.createRow();

            // 确保目标行有足够的单元格
            while (targetRow.getTableCells().size() < sourceRow.getTableCells().size()) {
                targetRow.addNewTableCell();
            }

            // 复制单元格内容和样式
            for (int j = 0; j < sourceRow.getTableCells().size(); j++) {
                XWPFTableCell sourceCell = sourceRow.getCell(j);
                XWPFTableCell targetCell = targetRow.getCell(j);

                // 复制单元格属性
                CTTcPr sourceTcPr = sourceCell.getCTTc().getTcPr();
                if (sourceTcPr != null) {
                    targetCell.getCTTc().setTcPr((CTTcPr) sourceTcPr.copy());
                }

                // 复制段落
                targetCell.getParagraphs().clear();
                for (XWPFParagraph sourcePara : sourceCell.getParagraphs()) {
                    XWPFParagraph targetPara = targetCell.addParagraph();
                    // 复制段落属性和内容...
                }
            }
        }

        return targetTable;
    }

    // 查找占位符后的表格
    private XWPFTable findTableAfterPlaceholder(XWPFDocument doc, String placeholder) {
        boolean foundPlaceholder = false;

        for (IBodyElement element : doc.getBodyElements()) {
            if (element instanceof XWPFParagraph) {
                XWPFParagraph para = (XWPFParagraph) element;
                if (para.getText().contains(placeholder)) {
                    foundPlaceholder = true;
                }
            } else if (element instanceof XWPFTable && foundPlaceholder) {
                return (XWPFTable) element;
            }
        }

        return null;
    }

    // 在目标文档中占位符后插入表格
    private void insertTableAfterPlaceholder(XWPFTable table, String placeholder) {
        for (int i = 0; i < targetDocument.getParagraphs().size(); i++) {
            XWPFParagraph para = targetDocument.getParagraphs().get(i);
            if (para.getText().contains(placeholder)) {
                // 移除占位符
                para.removeRun(0);
                para.createRun().setText("");

                // 插入表格
//                targetDocument.setTable( 1, table);
                targetDocument.insertTable(35, table);
                break;
            }
        }
    }



//    private void insertTableAfterPlaceholder(XWPFTable table, String placeholder) {
//        for (int i = 0; i < targetDocument.getParagraphs().size(); i++) {
//            XWPFParagraph para = targetDocument.getParagraphs().get(i);
//            // 查找占位符段落
//            XmlCursor cursor= para.getCTP().newCursor();
////            XWPFTable table = targetDocument.insertNewTbl(cursor);
////
////            setTableLocation(table,"center");
////            setCellLocation(table,"CENTER","center");
//            targetDocument.removeBodyElement(targetDocument.getPosOfParagraph(para));
//        }

//    }

    // 获取单元格值作为字符串
    private String getCellValueAsString(Cell cell) {
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    // 保存目标文档
    public void saveDocument(String outputPath) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            targetDocument.write(fos);
        }
    }

    // 表格操作方法示例
    public void modifyTable(String placeholder, TableModifier modifier) {
        XWPFTable table = findTableAfterPlaceholder(targetDocument, placeholder);
        if (table != null) {
            modifier.modify(table);
        }
    }

    // 表格操作接口
    public interface TableModifier {
        void modify(XWPFTable table);
    }
}