package com.gwssi.focus.reporting.auditreport.impl;

import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/21 16:31
 * @Description: 能耗指标与出货量合理性审查指标详情
 */
@Component
public class EnergyConsumptionAndShipmentsCensorExcel implements BusinessProcessing {


    @Override
    public boolean process(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        IPersistenceDAO daoDWD = DAOManager.getPersistenceDAO("DB_DWD");
        SqlHelper sql = new SqlHelper();
        sql.append("select * from DWD_FACTORY_INSPECTION_ENERGY_ONSUMPTION where name = ? ",parameters.get("company"));
        List<Map> maps = daoDWD.queryForList(sql.getSql(), sql.getParamList());
        if (maps.isEmpty()){
            return false;
        }
        for (int i = 0; i < maps.size(); i++) {
            XWPFTableRow row = table.createRow();
            row.getCell(0).setText(String.valueOf(maps.get(i).get("statYearMonth")));
            row.getCell(1).setText(String.valueOf(maps.get(i).get("sgr")));
            row.getCell(2).setText(String.valueOf(maps.get(i).get("egr")));
            row.getCell(3).setText("");
            row.getCell(4).setText("");
        }
        return true;
    }
}