package com.gwssi.focus.reporting;

import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlException;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTbl;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.List;

public class WordTableInserter {

    public static void main(String[] args) throws Exception {
        // 打开主文档
        XWPFDocument mainDoc = new XWPFDocument(new FileInputStream("D:\\gwdata\\验厂模型汇报-0515.docx"));

        // 打开包含表格的源文档
        XWPFDocument sourceDoc = new XWPFDocument(new FileInputStream("D:\\gwdata\\验厂模型汇报-0519沟通.docx"));

        // 获取源文档的第一个表格
        XWPFTable sourceTable = sourceDoc.getTables().get(0);

        // 查找插入点（例如在某个段落后面）
        for (int i = 0; i < mainDoc.getParagraphs().size(); i++) {
            XWPFParagraph paragraph = mainDoc.getParagraphs().get(i);
            if (paragraph.getText().contains("${excelData}")) {
                insertTableAfterParagraph(mainDoc, sourceTable, i,paragraph);
                break;
            }
        }

        // 保存修改后的主文档
        try (FileOutputStream out = new FileOutputStream("D:\\gwdata\\验厂模型汇报-0515完.docx")) {
            mainDoc.write(out);
        }

        System.out.println("表格插入完成并已保存！");
    }

    private static void insertTableAfterParagraph(XWPFDocument doc, XWPFTable sourceTable, int paragraphIndex,XWPFParagraph paragraph) throws XmlException, IOException {
        // 获取底层 XML 对象
//        CTTbl sourceCTTbl = sourceTable.getCTTbl();
//        CTTbl newTbl = CTTbl.Factory.parse(sourceCTTbl.newInputStream());
        // 获取段落的CTP对象
        CTP ctp = paragraph.getCTP();

        // 创建新表格并正确关联到文档
        CTTbl newCTTbl = doc.getDocument().getBody().addNewTbl();

        // 创建表格属性
        CTTblPr tblPr = newCTTbl.addNewTblPr();
        tblPr.addNewTblW().setW(BigInteger.valueOf(5000)); // 默认宽度

        // 在段落之后插入新表格的XML
        ctp.getDomNode().getParentNode().insertBefore(
                newCTTbl.getDomNode(),
                ctp.getDomNode().getNextSibling()
        );

//        sourceTable.get
        // 创建新表格对象
//        XWPFTable insertedTable = new XWPFTable(newTbl, doc);

        XWPFTable table = doc.createTable(3, 3);

        // 创建3行
        for (int rowIndex = 0; rowIndex < 3; rowIndex++) {
            XWPFTableRow row = (rowIndex == 0) ? table.getRow(0) : table.createRow();

            // 确保每行有3个单元格
            for (int colIndex = 0; colIndex < 3; colIndex++) {
                // 如果单元格不存在，创建它
                if (row.getTableCells().size() <= colIndex) {
                    row.addNewTableCell();
                }

                // 获取并设置单元格内容
                XWPFTableCell cell = row.getCell(colIndex);
                if (cell != null) {
                    cell.setText("Row " + (rowIndex + 1) + ", Col " + (colIndex + 1));
                }
            }
        }



        // 插入表格到指定段落后
        doc.insertTable(paragraphIndex + 1, sourceTable);

        // 表格处理（可选）
        editInsertedTable(table);
    }

    private static void editInsertedTable(XWPFTable table) {
        // 1. 删除某一列（比如第2列）
        deleteColumn(table, 1);

        // 2. 添加一列（在最后添加）
        addColumn(table);

        // 3. 修改单元格内容
        table.getRow(0).getCell(0).setText("修改后的内容");

        // 4. 合并第一行的前两个单元格
        mergeCellsHorizontally(table, 0, 0, 1);
    }

    // 删除某一列
    public static void deleteColumn(XWPFTable table, int colPosition) {
        for (XWPFTableRow row : table.getRows()) {
            List<XWPFTableCell> cells = row.getTableCells();
            if (colPosition < cells.size()) {
                cells.remove(colPosition);
            }
        }
    }

    // 添加一列
    public static void addColumn(XWPFTable table) {
        for (XWPFTableRow row : table.getRows()) {
            XWPFTableCell cell = row.addNewTableCell();
            cell.setText("新列");
        }
    }

    // 合并单元格（水平方向）
    public static void mergeCellsHorizontally(XWPFTable table, int row, int fromCol, int toCol) {
        XWPFTableCell cell = table.getRow(row).getCell(fromCol);
        CTTc ctTc = cell.getCTTc();
        ctTc.addNewTcPr().addNewGridSpan().setVal(BigInteger.valueOf(toCol - fromCol + 1));

        // 删除被合并的单元格
        for (int col = fromCol + 1; col <= toCol; col++) {
            table.getRow(row).removeCell(col);
        }
    }
}