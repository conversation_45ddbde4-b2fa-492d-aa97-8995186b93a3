package com.gwssi.focus.reporting.utils;

/**
 * <AUTHOR>
 * @Date 2025/6/6 9:19
 * @Description: ArtisticTextReplacer
 */
import org.apache.poi.xwpf.model.XWPFHeaderFooterPolicy;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.Arrays;
import java.util.List;

public class ArtisticTextReplacer {


    public static void main(String[] args) throws Exception {
        String inputPath = "D:\\gwdata\\验厂模型汇报-九影（深圳）电器有限公司.docx";
        String outputPath = "D:\\gwdata\\output1.docx";
        String oldText = "companyName";
        String newText = "新艺术字";

        try (XWPFDocument document = new XWPFDocument(new FileInputStream(inputPath))) {
            replaceArtisticText(document, oldText, newText);
            document.write(new FileOutputStream(outputPath));
        }
    }

    /**
     * 替换文档中的艺术字文本
     */
    public static void replaceArtisticText(XWPFDocument document, String oldText, String newText) {
        // 遍历段落
        for (XWPFParagraph para : document.getParagraphs()) {
            replaceArtisticTextInRuns(para.getRuns(), oldText, newText);
        }

        // 遍历页眉
//        XWPFHeaderFooterPolicy headerPolicy = new XWPFHeaderFooterPolicy(document);
//        XWPFHeader header = headerPolicy.getFirstPageHeader();
//        for (XWPFParagraph para : header.getParagraphs()) {
//            replaceArtisticTextInRuns(para.getRuns(), oldText, newText);
//        }
//
//
//        // 遍历页脚
//        XWPFFooter footer = headerPolicy.getFirstPageFooter();
//        for (XWPFParagraph para : footer.getParagraphs()) {
//            replaceArtisticTextInRuns(para.getRuns(), oldText, newText);
//        }

    }

    /**
     * 替换段落中所有运行（Run）的艺术字文本
     */
    private static void replaceArtisticTextInRuns(List<XWPFRun> runs, String oldText, String newText) {
        for (XWPFRun run : runs) {
            CTR ctr = run.getCTR();
            if (isArtisticText(ctr)) {
                replaceTextInCTR(ctr, oldText, newText);
            }
        }
    }

    /**
     * 判断是否为艺术字（核心逻辑）
     */
    private static boolean isArtisticText(CTR ctr) {
        // 条件1：存在默认运行属性（艺术字必有）
        if (!ctr.isSetRPr()) return false;
        CTRPr rPr = ctr.getRPr();

        // 条件2：字体为系统艺术字体（根据实际文档调整）
        CTFonts fonts = rPr.getRFonts();
        if (fonts != null) {
            String asciiFont = fonts.getAscii(); // 英文艺术字体名（如"STXingkai"）
            String hAnsiFont = fonts.getHAnsi(); // 中文艺术字体名（如"华文行楷"）

            // 常见艺术字体列表（需根据实际文档调整）
            List<String> artisticFonts = Arrays.asList(
                    "华文行楷", "汉仪卡通体", "方正舒体", "华文琥珀", "STXingkai"
            );
            if (asciiFont != null && artisticFonts.contains(asciiFont)) return true;
            if (hAnsiFont != null && artisticFonts.contains(hAnsiFont)) return true;
        }

        // 条件3：包含替代文本（可选，增强判断）
//        if (ctr.text) return true; // POI 5.x+ 推荐使用 containsAltText()

        return true;
    }

    private static boolean isArtisticText2(CTR ctr) {
        // 条件1：存在默认运行属性（艺术字通常有此标记）
        if (ctr.isSetRPr()) {
            return true;
        }
        // 条件2：存在替代文本（艺术字常用altText存储提示）
//        if (ctr.isSetAltText()) {
//            return true;
//        }
        // 条件3：字体为系统艺术字体（可选，根据实际文档调整）
        CTRPr rPr = ctr.getRPr();
        if (rPr != null && rPr.isSetRFonts()) {
            String fontName = rPr.getRFonts().getAscii();
            List<String> artisticFonts = Arrays.asList(
                    "华文行楷", "汉仪卡通体", "方正舒体", "华文琥珀", "STXingkai"
            );
            if (artisticFonts.contains(fontName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 替换 CTR 中的文本内容（核心方法）
     */
    private static void replaceTextInCTR(CTR ctr, String oldText, String newText) {
        // 提取当前所有文本内容（可能跨多个 <w:t> 元素）
        StringBuilder currentText = new StringBuilder();
        for (CTText t : ctr.getTArray()) { // CTR.getTArray() 返回所有 <w:t> 元素
            currentText.append(t.getStringValue());
        }

        // 替换目标文本
        String replacedText = currentText.toString().replace(oldText, newText);
        if (replacedText.equals(currentText.toString())) return; // 无替换需求

        // 清空原文本（保留第一个 <w:t> 元素，删除其他冗余片段）
        int tCount = ctr.getTArray().length;
        for (int i = tCount - 1; i > 0; i--) {
            ctr.removeT(i); // 移除第i个 <w:t> 元素（索引从1开始）
        }

        // 更新第一个 <w:t> 元素的文本
        CTText firstT = ctr.getTArray(0); // 获取第一个 <w:t> 元素（索引0）
        firstT.setStringValue(replacedText); // 设置新文本
    }
}
