package com.gwssi.focus.reporting.auditreport.impl;

import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/21 16:31
 * @Description: 股权冻结情况
 */
@Component
public class FreezeEntryExcel implements BusinessProcessing {

    @Override
    public boolean process(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        IPersistenceDAO daoODS = DAOManager.getPersistenceDAO("DB_ODS");
        SqlHelper sql = new SqlHelper();
        sql.append("select d.*,z.company from ODS_CRM_OPPORTUNITY_MANAGEMENT z left join ODS_CRM_OPPORTUNITY_MANAGEMENT_LINK_FREEZE_ENTRY d on z.id = d.parent_id" +
                " where z.company = ? ",parameters.get("company"));
        List<Map> maps = daoODS.queryForList(sql.getSql(), sql.getParamList());
        if (maps.isEmpty()){
            return false;
        }
        for (int i = 0; i < maps.size(); i++) {
            XWPFTableRow row = table.createRow();
            ensureRowHasCells(row, table.getRow(0).getTableCells().size());
            row.getCell(0).setText(String.valueOf(i+1));
            row.getCell(1).setText(String.valueOf(maps.get(i).get("company")));
            row.getCell(2).setText(String.valueOf(maps.get(i).get("executeName")));
            row.getCell(3).setText(String.valueOf(maps.get(i).get("equityAmount")));
            row.getCell(4).setText(String.valueOf(maps.get(i).get("executeCourt")));
            row.getCell(5).setText(String.valueOf(maps.get(i).get("executionNoticeNum")));
            row.getCell(6).setText(String.valueOf(maps.get(i).get("status")));
            row.getCell(7).setText(String.valueOf(maps.get(i).get("relatedInfoName")));
        }
        return true;
    }


    /**
     * 确保行有足够的单元格
     */
    private static void ensureRowHasCells(XWPFTableRow row, int cellCount) {
        while (row.getTableCells().size() < cellCount) {
//            row.addNewTableCell();
            row.createCell();
        }
    }
}