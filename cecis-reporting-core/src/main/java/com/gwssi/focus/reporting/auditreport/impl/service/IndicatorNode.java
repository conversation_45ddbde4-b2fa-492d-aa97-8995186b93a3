package com.gwssi.focus.reporting.auditreport.impl.service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/5/21 20:17
 * @Description: IndicatorNode
 */
public class IndicatorNode {
    public String indicatorNumber;
    public String indicatorName;
    String indicatorValueStr;
    String promptInformation;
    String indicatorHierarchy;
    public List<IndicatorNode> children = new ArrayList<>();

    public IndicatorNode(String number, String name, String hierarchy, String indicatorValueStr, String promptInformation) {
        this.indicatorNumber = number;
        this.indicatorName = name;
        this.indicatorHierarchy = hierarchy;
        this.indicatorValueStr = indicatorValueStr;
        this.promptInformation = promptInformation;
    }
}
