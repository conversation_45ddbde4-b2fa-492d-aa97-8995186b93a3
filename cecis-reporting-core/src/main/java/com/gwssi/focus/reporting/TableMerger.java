package com.gwssi.focus.reporting;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

public class TableMerger {

    public static void mergeSameCells(XWPFTable table) {
        int rowCount = table.getNumberOfRows();
        if (rowCount == 0) return;

        int colCount = table.getRow(0).getTableCells().size();
        boolean[][] mergedCells = new boolean[rowCount][colCount];

        for (int i = 0; i < rowCount; i++) {
            XWPFTableRow row = table.getRow(i);
            if (row == null) continue;

            for (int j = 0; j < colCount; j++) {
                if (mergedCells[i][j]) continue;

                XWPFTableCell currentCell = row.getCell(j);
                if (currentCell == null) continue;

                String content = currentCell.getText().trim();
                if (content.isEmpty()) continue;

                int maxCol = j;
                int maxRow = i;

                // 寻找最大合并范围
                boolean validRegion = true;
                // 横向扩展
                while (maxCol + 1 < colCount) {
                    XWPFTableCell rightCell = row.getCell(maxCol + 1);
                    if (rightCell == null || !rightCell.getText().trim().equals(content)) break;
                    maxCol++;
                }
                // 纵向扩展
                while (maxRow + 1 < rowCount) {
                    XWPFTableRow downRow = table.getRow(maxRow + 1);
                    if (downRow == null) break;
                    XWPFTableCell downCell = downRow.getCell(j);
                    if (downCell == null || !downCell.getText().trim().equals(content)) break;
                    maxRow++;
                }

                // 验证整个区域内容一致性
                validRegion = validateRegion(table, i, j, maxRow, maxCol, content);

                if (validRegion) {
                    mergeRegion(table, i, j, maxRow, maxCol);
                    markMerged(mergedCells, i, j, maxRow, maxCol);
                    j = maxCol; // 跳过已合并列
                } else {
                    // 尝试横向合并
                    if (maxCol > j) {
                        mergeHorizontal(table, i, j, maxCol);
                        markMerged(mergedCells, i, j, maxCol, j);
                        j = maxCol;
                    }
                    // 尝试纵向合并
                    if (maxRow > i) {
                        mergeVertical(table, i, j, maxRow);
                        markMerged(mergedCells, i, j, maxRow, j);
                        i = maxRow; // 跳过已合并行
                    }
                }
            }
        }
    }

    private static boolean validateRegion(XWPFTable table, int startRow, int startCol,
                                          int endRow, int endCol, String content) {
        for (int r = startRow; r <= endRow; r++) {
            XWPFTableRow row = table.getRow(r);
            if (row == null) return false;
            for (int c = startCol; c <= endCol; c++) {
                XWPFTableCell cell = row.getCell(c);
                if (cell == null || !cell.getText().trim().equals(content)) {
                    return false;
                }
            }
        }
        return true;
    }

    private static void mergeRegion(XWPFTable table, int startRow, int startCol,
                                    int endRow, int endCol) {
        for (int r = startRow; r <= endRow; r++) {
            XWPFTableRow row = table.getRow(r);
            if (row == null) continue;
            for (int c = startCol; c <= endCol; c++) {
                XWPFTableCell cell = row.getCell(c);
                if (cell == null) continue;
                CTTcPr tcPr = cell.getCTTc().getTcPr();
                if (tcPr == null) tcPr = cell.getCTTc().addNewTcPr();

                if (r == startRow && c == startCol) {
                    // 左上角单元格
                    CTVMerge vMerge = tcPr.addNewVMerge();
                    vMerge.setVal(STMerge.RESTART);
                    CTHMerge hMerge = tcPr.addNewHMerge();
                    hMerge.setVal(STMerge.RESTART);
                } else {
                    // 其他单元格
                    if (r == startRow) {
                        // 同行单元格
                        CTHMerge hMerge = tcPr.addNewHMerge();
                        hMerge.setVal(STMerge.CONTINUE);
                    }
                    if (c == startCol) {
                        // 同列单元格
                        CTVMerge vMerge = tcPr.addNewVMerge();
                        vMerge.setVal(STMerge.CONTINUE);
                    }
                }
            }
        }
    }

    private static void mergeHorizontal(XWPFTable table, int row, int startCol, int endCol) {
        XWPFTableCell firstCell = table.getRow(row).getCell(startCol);
        for (int c = startCol + 1; c <= endCol; c++) {
            XWPFTableCell cell = table.getRow(row).getCell(c);
            if (cell == null) continue;

            // 合并属性
            CTTcPr tcPr = firstCell.getCTTc().getTcPr();
            if (tcPr == null) tcPr = firstCell.getCTTc().addNewTcPr();

            CTHMerge hMerge = tcPr.addNewHMerge();
            hMerge.setVal(STMerge.RESTART);

            // 正确方式：清除被合并单元格的内容
            for (int i = cell.getParagraphs().size() - 1; i >= 0; i--) {
                cell.removeParagraph(i);
            }

            // 移动内容
            firstCell.getParagraphs().addAll(cell.getParagraphs());
        }
    }

    private static void mergeVertical(XWPFTable table, int col, int startRow, int endRow) {
        XWPFTableCell firstCell = table.getRow(startRow).getCell(col);
        for (int r = startRow + 1; r <= endRow; r++) {
            XWPFTableCell cell = table.getRow(r).getCell(col);
            if (cell == null) continue;

            // 合并属性
            CTTcPr tcPr = firstCell.getCTTc().getTcPr();
            if (tcPr == null) tcPr = firstCell.getCTTc().addNewTcPr();

            CTVMerge vMerge = tcPr.addNewVMerge();
            vMerge.setVal(STMerge.RESTART);

            // 正确方式：清除被合并单元格的内容
            for (int i = cell.getParagraphs().size() - 1; i >= 0; i--) {
                cell.removeParagraph(i);
            }

            // 移动内容
            firstCell.getParagraphs().addAll(cell.getParagraphs());
        }
    }

    private static void markMerged(boolean[][] mergedCells, int startRow, int startCol,
                                   int endRow, int endCol) {
        for (int r = startRow; r <= endRow; r++) {
            for (int c = startCol; c <= endCol; c++) {
                mergedCells[r][c] = true;
            }
        }
    }
}