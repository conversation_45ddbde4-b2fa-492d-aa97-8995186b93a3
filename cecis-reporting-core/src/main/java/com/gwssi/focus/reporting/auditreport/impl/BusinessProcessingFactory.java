package com.gwssi.focus.reporting.auditreport.impl;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/21 17:27
 * @Description: BusinessProcessingFactory
 */
@Component
public class BusinessProcessingFactory {
    private final Map<String, BusinessProcessing> strategies = new HashMap<>();

    public BusinessProcessingFactory(Map<String, BusinessProcessing> strategyMap) {
        this.strategies.putAll(strategyMap);
    }

    public BusinessProcessing getStrategy(String strategyName) {
        return strategies.get(strategyName);
    }
}
