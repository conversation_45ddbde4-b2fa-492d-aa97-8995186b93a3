package com.gwssi.focus.reporting.auditreport.impl.service;

import com.gwssi.focus.reporting.auditreport.impl.BusinessProcessing;
import com.gwssi.focus.reporting.utils.WordProcessorUtil;
import com.gwssi.optimus.core.exception.OptimusException;
import com.gwssi.optimus.core.persistence.dao.DAOManager;
import com.gwssi.optimus.core.persistence.dao.IPersistenceDAO;
import com.gwssi.optimus.core.util.SqlHelper;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/21 16:31
 * @Description: 工厂主要产品进项物料信息top10
 */
@Component
public class Top10InputMaterialExcel implements BusinessProcessing {

    @Override
    public boolean process(XWPFTable table, Map<String, Object> parameters) throws OptimusException {
        IPersistenceDAO daoODS = DAOManager.getPersistenceDAO("dataSource_DB_ODS");
        SqlHelper sql = new SqlHelper();
        sql.append("select goods_taxable_services,sum(total_amount_including_tax) as total_amount_including_tax FROM\n" +
                "(select *\n" +
                "  from db_ods.ods_input_invoice\n" +
                " where  goods_taxable_services not like '%电费%'\n" +
                "   and goods_taxable_services not like '%电服务费%'\n" +
                "   and goods_taxable_services not like '%水费%'\n" +
                "   and goods_taxable_services not like '%水服务费%'\n" +
                "   and invoice_date >= ADD_MONTHS(SYSDATE, -6)\n" +
                "   and purchaser_name = ?)\n" +
                "GROUP by goods_taxable_services\n" +
                "order by total_amount_including_tax desc\n" +
                "limit 10",parameters.get("company"));
        List<Map> maps = daoODS.queryForList(sql.getSql(), sql.getParamList());
        if (maps.isEmpty()){
            return false;
        }
        for (int i = 0; i < maps.size(); i++) {
            XWPFTableRow row = WordProcessorUtil.createRow(table);
            row.getCell(0).setText(String.valueOf(i+1));
            row.getCell(1).setText(String.valueOf(maps.get(i).get("goodsTaxableServices")));
            row.getCell(2).setText(String.valueOf(maps.get(i).get("totalAmountIncludingTax")));
            row.getCell(3);
        }
        return true;
    }
}