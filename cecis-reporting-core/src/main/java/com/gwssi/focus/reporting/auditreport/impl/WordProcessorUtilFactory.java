package com.gwssi.focus.reporting.auditreport.impl;

import com.gwssi.focus.reporting.utils.WordProcessorUtil;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/5/21 17:18
 * @Description: WordProcessorUtilFactory
 */
@Component
public class WordProcessorUtilFactory {
    private final Map<String, WordProcessorUtil> instances = new HashMap<>();

    public WordProcessorUtil getInstance(String templatePath) throws IOException {
        if (instances.containsKey(templatePath)) {
            instances.remove(templatePath);
        }
        instances.put(templatePath, new WordProcessorUtil());
        return instances.get(templatePath);
    }
}
