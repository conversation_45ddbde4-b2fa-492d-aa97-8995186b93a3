package com.gwssi.focus.reporting.utils;

import java.math.BigDecimal; /**
 * <AUTHOR>
 * @Date 2025/5/23 10:09
 * @Description: BigDecimalUtils
 */
public class BigDecimalUtils {
    // 安全累加，处理null值
    public static BigDecimal safeAdd(BigDecimal a, BigDecimal b) {
        return (a == null ? BigDecimal.ZERO : a).add(b == null ? BigDecimal.ZERO : b);
    }

    // 格式化输出
    public static String format(BigDecimal value, int scale) {
        return value.setScale(scale, BigDecimal.ROUND_HALF_UP).toPlainString();
    }
}
