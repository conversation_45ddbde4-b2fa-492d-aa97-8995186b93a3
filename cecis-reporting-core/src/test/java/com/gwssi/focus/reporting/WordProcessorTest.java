package com.gwssi.focus.reporting;

import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.junit.Test;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2025/5/16 11:08
 * @Description: WordProcessorTest
 */
public class WordProcessorTest {
//    @Test
//    public void testProcessTemplate() throws Exception {
//        // 模板路径
//        String templatePath = "D:\\gwdata\\验厂模型汇报-0515.docx";
//        // 输出路径
//        String outputPath = "D:\\gwdata\\验厂模型汇报-0515完.docx";
//        // 图片路径
//        String imagePath1 = "D:\\gwdata\\logo1.jpg";
//        String imagePath2 = "D:\\gwdata\\logo2.jpg";
//        // PDF路径
//        String pdfPath = "D:\\gwdata\\pdf.pdf";
//
//        WordTemplateProcessor processor = new WordTemplateProcessor(templatePath);
//        Random rand = new Random();
//        // 替换文本
//        processor.replaceText("${companyName}", "新长城科技有限公司");
//        processor.replaceText("${reportDate}", "2025年5月16日");
//        processor.replaceText("${reportYear}", "2025");
//        processor.replaceText("${reportRandom}", String.valueOf(rand.nextInt(3000)));
////        processor.replaceText("${totalAmount}", "1,234,567.89");
//
//        // 替换图片
//        processor.replaceImage("${logo1}", imagePath1, 200, 100);
//        processor.replaceImage("${logo2}", imagePath2, 400, 200);
//
//        // 替换表格
//        List<List<String>> tableData = Arrays.asList(
//                Arrays.asList("序号", "产品名称", "数量", "单价"),
//                Arrays.asList("1", "笔记本电脑", "10", "¥5,999.00"),
//                Arrays.asList("2", "打印机", "5", "¥1,299.00"),
//                Arrays.asList("3", "显示器", "15", "¥1,799.00")
//        );
//        processor.replaceTable("${productTable}", tableData, 4);
//
//        // 插入PDF内容
//        processor.insertPdfAsImages("${pdfContent}", pdfPath);
//
//        // 保存文档
//        processor.save(outputPath);
//        System.out.println("文档生成完成: " + outputPath);
//    }

    @Test
    public void testProcessDocument() throws IOException, InvalidFormatException {
        // 模板路径和输出路径
        String templatePath = "D:\\gwdata\\验厂模型汇报-0519.docx";
        String outputPath = "D:\\gwdata\\验厂模型汇报-0519完.docx";
        WordProcessor processor = new WordProcessor(templatePath);
        Random rand = new Random();



        // 插入表格（从word读取）
//        processor.importWordTable("${screeningResults}", "D:\\gwdata\\验厂模型筛查结果表格.docx",0);
        processor.importWordTable("${incomingInvoices}", "D:\\gwdata\\发票明细表格.docx",0);

        // 替换文本
        processor.replaceText("${companyName}", "新长城科技有限公司");
        processor.replaceText("${reportDate}", "2025年5月19日");
        processor.replaceText("${reportYear}", "2025");
        processor.replaceText("${reportRandom}", String.valueOf(rand.nextInt(3000)));
        processor.replaceText("${companySocial}", "AAABBBCCCDDDEEEFFF");
        processor.replaceText("${taskNumber}", "YC-ACD-20250519");
        processor.replaceText("${conclusion}", "通过");
        processor.replaceText("${managementState}", "存续");
        processor.replaceText("${isConsistency}", "是");
        processor.replaceText("${checkResult1}", "通过");
        processor.replaceText("${checkResult2}", "不通过");
        processor.replaceText("${checkResult3}", "通过");
        processor.replaceText("${checkResult4}", "不通过");
        processor.replaceText("${checkResult5}", "啊啊啊啊啊啊啊啊啊啊");


        // 替换图片
        processor.replaceImage("${taxPaymentCertificate}", "D:\\gwdata\\完税证明.jpg", 200, 200);
//        processor.replaceImage("${pdfContent2}", new FileInputStream("D:\\gwdata\\pdf.png"), 200, 200);

        // 插入PDF第一页内容（转图片）
        processor.insertPdfFirstPageAsImage("${taxReturns}", "D:\\gwdata\\纳税申报.pdf",200,300);

        // 艺术字处理替换（还不成功）
        processor.replaceWordArtText("${companyName}","新长城科技有限公司");




//        // 替换excel（从excel读取）
//        processor.importExcelTable("${excelData}", "D:\\gwdata\\excel.xlsx", "Sheet1");

//        // 替换表格(自建表格)
//        List<List<String>> tableData = Arrays.asList(
//                Arrays.asList("姓名", "年龄", "职业"),
//                Arrays.asList("张三", "30", "工程师"),
//                Arrays.asList("李四", "25", "设计师")
//        );
//        processor.replaceTable("${productTable}", tableData);

//        // 插入PDF内容（只有文字，没有样式）
//        processor.insertPdfContent("${pdfContent}", "D:\\gwdata\\pdf.pdf");

        // 保存文档
        processor.save(outputPath);

    }

}
