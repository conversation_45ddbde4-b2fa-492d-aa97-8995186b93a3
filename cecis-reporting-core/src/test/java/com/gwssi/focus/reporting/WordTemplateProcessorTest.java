package com.gwssi.focus.reporting;

import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.junit.Test;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.*;

public class WordTemplateProcessorTest {

    @Test
    public void testProcessTemplate() throws Exception {


        // 1. 加载模板
        XWPFDocument doc = new XWPFDocument(
                new FileInputStream("D:\\gwdata\\验厂模型汇报-0515.docx"));

        // 2. 准备测试数据
        Map<String, String> imageMap = new HashMap<>();
        imageMap.put("${logo2}", "D:\\gwdata\\logo2.jpg");
        imageMap.put("${logo1}", "D:\\gwdata\\logo1.jpg");
        imageMap.put("${logo22}", "D:\\gwdata\\logo2.jpg");
        imageMap.put("${abc}", "D:\\gwdata\\logo2.jpg");
        imageMap.put("${abc21}", "D:\\gwdata\\logo2.jpg");
        imageMap.put("${logoo}", "D:\\gwdata\\logo2.jpg");
        imageMap.put("${logoh}", "D:\\gwdata\\logo2.jpg");
        imageMap.put("${adasdasdlogo22}", "D:\\gwdata\\logo2.jpg");



        List<String[]> tableData = Arrays.asList(
                new String[]{"Header1", "Header2"},
                new String[]{"Data1", "Data2"}
        );
        Random rand = new Random();
        Map<String, String> textMap = new HashMap<>();
        textMap.put("${text1}", "替换后的文本内容");
        textMap.put("${companyName}", "新长城科技有限公司");
        textMap.put("${reportDate}", "2025年5月16日");
        textMap.put("${reportYear}", "2025");
        textMap.put("${reportRandom}", String.valueOf(rand.nextInt(3000)));
        textMap.put("${checkResult}", "通过");

        // 3. 执行替换操作
        WordTemplateProcessor.replaceImages(doc, imageMap);
        WordTemplateProcessor.createTables(doc, Collections.singletonList(new String[]{"动态表格"}));
        WordTemplateProcessor.replaceText(doc, textMap);
        WordTemplateProcessor.insertPdfContent(doc, "D:\\gwdata\\pdf.pdf");
        WordTemplateProcessor.insertExcelTable(doc, "D:\\gwdata\\excel.xlsx");
//        WordTemplateProcessor.insertWordArt(doc, "动态艺术字");

        // 4. 保存结果
        FileOutputStream out = new FileOutputStream("D:\\gwdata\\验厂模型汇报-0515完.docx");
        doc.write(out);
        out.close();
        doc.close();

//        // 模板文件路径
//        String templatePath = "D:\\gwdata\\验厂模型汇报-0515.docx";
//        String outputPath = "D:\\gwdata\\验厂模型汇报-0515完.docx";
//        Random rand = new Random();
//        WordTemplateProcessor processor = new WordTemplateProcessor(templatePath);
//
//        // 1. 替换文本
//        processor.replaceText("${companyName}", "新长城科技有限公司");
//        processor.replaceText("${reportDate}", "2025年5月16日");
//        processor.replaceText("${reportYear}", "2025");
//        processor.replaceText("${reportRandom}", String.valueOf(rand.nextInt(3000)));
//        processor.replaceText("${checkResult}", String.valueOf("通过"));
//
//        // 2. 替换图片
//        processor.replaceImage("${logo2}", "D:\\gwdata\\logo2.jpg", 200, 200);
//        processor.replaceImage("${logo1}", "D:\\gwdata\\logo1.jpg", 400, 300);
//        processor.replaceImage("${logo22}", "D:\\gwdata\\logo2.jpg", 200, 200);
//        processor.replaceImage("${abc}", "D:\\gwdata\\logo2.jpg", 200, 200);
//        processor.replaceImage("${abc21}", "D:\\gwdata\\logo2.jpg", 200, 200);
//        processor.replaceImage("${logoo}", "D:\\gwdata\\logo2.jpg", 200, 200);
//        processor.replaceImage("${logoh}", "D:\\gwdata\\logo2.jpg", 200, 200);
//        processor.replaceImage("${adasdasdlogo22}", "D:\\gwdata\\logo2.jpg", 200, 200);
//        // 3. 插入动态表格
//        List<List<String>> tableData = Arrays.asList(
//                Arrays.asList("产品名称", "数量", "单价", "金额"),
//                Arrays.asList("电脑", "5", "5000", "25000"),
//                Arrays.asList("打印机", "2", "2000", "4000"),
//                Arrays.asList("服务器", "1", "15000", "15000")
//        );
//        processor.insertTableAfterPlaceholder("${productTable}", tableData, 4);
//
//        // 4. 插入PDF内容
//        processor.insertPdfContent("${pdfContent}", "D:\\gwdata\\pdf.pdf");
//
//        // 5. 导入Excel表格
//        processor.importExcelTable("${excelData}", "D:\\gwdata\\excel.xlsx", "Sheet1");
//
//        // 保存处理后的文档
//        processor.saveDocument(outputPath);
//
//        // 验证输出文件是否存在
//        File outputFile = new File(outputPath);
//        if (outputFile.exists()) {
//            System.out.println("文档处理成功，输出文件: " + outputPath);
//        } else {
//            System.err.println("文档处理失败，输出文件不存在");
//        }
    }
}